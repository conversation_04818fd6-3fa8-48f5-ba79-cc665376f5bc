<?php

declare(strict_types=1);

namespace App\Hexagonal\Forecast\Adapters\Database;

use App\Entity\AdManagerDevice;
use App\Hexagonal\Forecast\Domain\Database\AdManagerDeviceRepositoryPort;
use Doctrine\Persistence\ManagerRegistry;

final class AdManagerDeviceRepositoryPSQLAdapter implements AdManagerDeviceRepositoryPort
{
    private ManagerRegistry $managerRegistry;

    public function __construct(ManagerRegistry $managerRegistry)
    {
        $this->managerRegistry = $managerRegistry;
    }

    /**
     * @inheritDoc
     */
    public function getByIds(array $ids): array
    {
        $adUnitRepository = $this->managerRegistry->getRepository(AdManagerDevice::class);
        return $adUnitRepository->findBy(['id' => $ids]);
    }
}