<?php

declare(strict_types=1);

namespace App\Entity;

use <PERSON>ymfony\Component\Serializer\Annotation\Groups;
use ApiPlatform\Core\Annotation\ApiResource;
use ApiPlatform\Core\Annotation\ApiProperty;
use ApiPlatform\Core\Action\NotFoundAction;
use Doctrine\ORM\Mapping as ORM;
use App\Repository\ClientRepository;

/**
 * @ApiResource(
 *     formats={"json"},
 *     collectionOperations={
 *        "get"={
 *            "method": "GET",
 *            "normalization_context"={"groups"={"clientsRead"}},
 *        },
 *     },
 *     itemOperations={"get"},
 * )
 *
 * @ORM\Entity(repositoryClass=ClientRepository::class)
 * @ORM\Table(name="clients")
 */
class Client
{
    /**
     * @ApiProperty(identifier=true)
     *
     * @ORM\Id()
     * @ORM\GeneratedValue()
     * @ORM\Column(type="integer")
     * @Groups({"clientsRead"})
     */
    private ?int $id;

    /**
     * @ORM\Column(name="client_id", type="string")
     * @Groups({"clientsRead"})
     */
    private string $clientId;

    /**
     * @ORM\Column(name="intensity", type="json_array")
     */
    private array $intensity;

    /**
     * @param ?int $id
     * @param string $clientId
     * @param array $intensity
     */
    public function __construct(?int $id, string $clientId, array $intensity)
    {
        $this->id = $id;
        $this->clientId = $clientId;
        $this->intensity = $intensity;
    }

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @return string
     */
    public function getClientId(): string
    {
        return $this->clientId;
    }

    /**
     * @param string $clientId
     */
    public function setClientId(string $clientId): void
    {
        $this->clientId = $clientId;
    }

    /**
     * @return array
     */
    public function getIntensity(): array
    {
        return $this->intensity;
    }

    /**
     * @param array $intensity
     */
    public function setIntensity(array $intensity): void
    {
        $this->intensity = $intensity;
    }
}