<?php

declare(strict_types=1);

namespace App\Hexagonal\Forecast\Domain\Service;

use App\Hexagonal\Forecast\Domain\Model\ForecastParams;
use App\Hexagonal\Product\Domain\Database\ProductRepositoryPort;
use App\Hexagonal\Forecast\Domain\Database\ForecastRepositoryPort;

final class ForecastService implements ForecastPort
{
    private ProductRepositoryPort $productRepository;
    private ForecastRepositoryPort $forecastRepository;
    private ForecastParams $forecastParams;

    public function __construct(ProductRepositoryPort $productRepository,
                                ForecastRepositoryPort $forecastRepository,
                                ForecastParams $forecastParams)
    {
        $this->productRepository = $productRepository;
        $this->forecastRepository = $forecastRepository;
        $this->forecastParams = $forecastParams;
    }

    /**
     * @inheritDoc
     */
    public function get(int $productId,
                        array $postalCode,
                        array $broadcastMedium,
                        array $projectType,
                        string $startDate,
                        string $endDate): array
    {
        $product = $this->productRepository->get($productId);
        $this->forecastParams->setParams($product, $postalCode, $broadcastMedium, $projectType, $startDate, $endDate);

        return $this->forecastRepository->get($this->forecastParams);
    }
}