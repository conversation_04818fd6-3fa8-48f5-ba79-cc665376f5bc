<?php

declare(strict_types=1);

namespace App\Hexagonal\Client\Adapters\Database;

use App\Hexagonal\Client\Domain\ClientRepositoryPort;
use App\Entity\Client;
use Doctrine\Persistence\ObjectRepository;
use Doctrine\Persistence\ManagerRegistry;

final class ClientRepositoryPSQLAdapter implements ClientRepositoryPort
{
    private ObjectRepository $clientRepository;

    /**
     * @param ManagerRegistry $managerRegistry
     */
    public function __construct(ManagerRegistry $managerRegistry)
    {
        $this->clientRepository = $managerRegistry->getRepository(Client::class);
    }

    public function getByClientId(string $clientId): ?Client
    {
        return $this->clientRepository->findOneBy(['clientId' => $clientId]);
    }

    /**
     * @inheritDoc
     */
    public function getAllByClientId(string $clientId): array
    {
        return $this->clientRepository->getAllByClientId($clientId);
    }

    /**
     * @inheritDoc
     */
    public function bulkSave(array $clients): void
    {
        $this->clientRepository->bulkSave($clients);
    }
}