<?php

declare(strict_types=1);

namespace App\Hexagonal\User\Domain\Database;

use Symfony\Component\Security\Core\User\UserInterface;

interface UserRepositoryPort
{
    public function create(UserInterface $user): UserInterface;

    public function getByEmail(string $email): ?UserInterface;

    public function get(int $id): ?UserInterface;

    public function getAll(): array;

    public function update(UserInterface $user): ?UserInterface;
}
