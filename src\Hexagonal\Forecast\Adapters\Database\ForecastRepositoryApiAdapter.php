<?php

declare(strict_types=1);

namespace App\Hexagonal\Forecast\Adapters\Database;

use App\Entity\Forecast;
use App\Hexagonal\Forecast\Domain\Database\ForecastRepositoryPort;
use App\Hexagonal\Forecast\Domain\Model\ForecastParams;
use App\Hexagonal\Places\Domain\PlaceRepositoryPort;

/**
 * This repository can switch between Google & Smart Adapter, depending on broadcastMedium value
 * By default : Google
 */
final class ForecastRepositoryApiAdapter implements ForecastRepositoryPort
{
    private ForecastRepositoryPort $googleForecastRepository;
    private ForecastRepositoryPort $smartForecastRepository;
    private PlaceRepositoryPort $placeRepository;
    private const SL_MEDIUM = 'seloger';

    public function __construct(ForecastRepositoryPort $googleForecastRepository,
                                ForecastRepositoryPort $smartForecastRepository,
                                PlaceRepositoryPort $placeRepository)
    {
        $this->googleForecastRepository = $googleForecastRepository;
        $this->smartForecastRepository = $smartForecastRepository;
        $this->placeRepository = $placeRepository;
    }

    /**
     * Return the matching repository by broadcastMedium
     * @param string $broadcastMedium
     * @return ForecastRepositoryPort
     */
    private function getRepositoryByBroadcastMedium(string $broadcastMedium): ForecastRepositoryPort
    {
        if ($broadcastMedium === self::SL_MEDIUM) {
            return $this->googleForecastRepository;
        } else {
            return $this->smartForecastRepository;
        }
    }

    /**
     * Get Forecast by adserver/params
     * @param ForecastParams $forecastParams
     * @return array
     */
    public function get(ForecastParams $forecastParams): array
    {
        $cumulativeResults = $this->getInitForecastResults($forecastParams->getPostalCode());
        foreach ($forecastParams->getBroadcastMedium() as $broadcastMedium) {
            $forecastResults = $this->getRepositoryByBroadcastMedium($broadcastMedium)->get($forecastParams);
            $cumulativeResults = $this->addForecastResults($cumulativeResults, $forecastResults);
        }

        return $this->toEntity($cumulativeResults);
    }

    /**
     * Get initial forecast results array
     * @param array $postalCodes
     * @return array
     */
    private function getInitForecastResults(array $postalCodes): array
    {
        $forecastResults = [];

        // retrieve names matching the codes
        $codes = $this->placeRepository->getByCodes($postalCodes);

        foreach ($postalCodes as $postalCode) {
            $forecastResults[$postalCode] = [
                'name' => $codes[$postalCode]->getName(),
                'code' => $codes[$postalCode]->getCode(),
                'matched' => 0,
                'available' => 0,
                'sold' => 0,
            ];
        }

        // sort by name
        uasort($forecastResults, function ($item1, $item2) {
            return $item1['name'] <=> $item2['name'];
        });

        return $forecastResults;
    }

    /**
     * Merge the forecastResults with the cumulativeResults
     * @param array $cumulativeResults
     * @param array $forecastResults
     * @return array
     */
    private function addForecastResults(array $cumulativeResults, array $forecastResults)
    {
        foreach ($forecastResults as $postalCode => $forecastResult) {
            $cumulativeResults[$postalCode]['matched'] += $forecastResult['matched'];
            $cumulativeResults[$postalCode]['available'] += $forecastResult['available'];
            $cumulativeResults[$postalCode]['sold'] += $forecastResult['sold'];
        }

        return $cumulativeResults;
    }

    /**
     * Exchange array of results into entity results
     * @param array $forecasts
     * @return array
     */
    private function toEntity(array $forecasts): array
    {
        $results = [];
        foreach ($forecasts as $forecast) {
            $results[] = new Forecast(
                $forecast['matched'],
                $forecast['available'],
                $forecast['sold'],
                $forecast['code'],
                trim($forecast['name']));
        }

        return $results;
    }
}