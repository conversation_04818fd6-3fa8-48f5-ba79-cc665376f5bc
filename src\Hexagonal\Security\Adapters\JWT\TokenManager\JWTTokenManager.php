<?php

declare(strict_types=1);

namespace App\Hexagonal\Security\Adapters\JWT\TokenManager;

use App\Hexagonal\Security\Domain\JWT\TokenManager\TokenManagerPort;
use Firebase\JWT\JWT;

final class JWTTokenManager implements TokenManagerPort
{
    /**
     * {@inheritdoc}
     */
    public function decode(string $jwt, string $key, array $algs)
    {
        return JWT::decode($jwt, $key, $algs);
    }

    /**
     * {@inheritdoc}
     */
    public function getKid(string $jwt): ?string
    {
        $tks = explode('.', $jwt);
        if (count($tks) !== 3) {
            return null;
        }

        list($headb64, $bodyb64, $cryptob64) = $tks;

        try {
            return JWT::jsonDecode(JWT::urlsafeB64Decode($headb64))->kid;
        } catch (\Throwable $t) {
            return null;
        }
    }
}
