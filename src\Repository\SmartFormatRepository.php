<?php

namespace App\Repository;

use App\Entity\SmartFormat;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method SmartFormat|null find($id, $lockMode = null, $lockVersion = null)
 * @method SmartFormat|null findOneBy(array $criteria, array $orderBy = null)
 * @method SmartFormat[]    findAll()
 * @method SmartFormat[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class SmartFormatRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, SmartFormat::class);
    }

}
