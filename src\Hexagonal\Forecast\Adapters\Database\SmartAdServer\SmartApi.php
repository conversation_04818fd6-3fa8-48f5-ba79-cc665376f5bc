<?php

declare(strict_types=1);

namespace App\Hexagonal\Forecast\Adapters\Database\SmartAdServer;

class SmartApi
{
    private const BASE_URI = 'https://forecast.smartadserverapis.com/';
    private const NETWORK_ID = 2751;

    private HttpRestClient $client;

    public function __construct(HttpRestClient $client, AuthInterface $auth)
    {
        $this->client = $client;
        $this->client->setAuth($auth->getAuth());
    }

    /**
     * POST : create a URI resource and return location header
     *
     * @param array $query
     * @return string
     */
    private function post(array $query): string
    {
        $response = $this->client->post(self::BASE_URI . self::NETWORK_ID . '/forecast/', $query);

        return $response['headers']['location'][0];
    }

    /**
     * Check if status Available, if not the location header will be absent
     * @param string $status
     * @return bool
     */
    private function isStatusAvailable(string $status): bool
    {
        if ($status === 'Available') {
            return true;
        }
        return false;
    }

    /**
     * GET : fetch the location header from POST and return location header (csv uri)
     *
     * @param string $link
     * @return string
     */
    private function getCsvUri(string $link): string
    {
        // GET : use the resource (from POST) to get the CSV URI in location header
        $response = $this->client->get($link);

        // wait for status available
        usleep(400000);

        // retry max 3 times : check if Status Available
        $maxRetries = 3;
        $retryCount = 0;
        $isAvailable = $this->isStatusAvailable($response['content']['status']);

        while (!$isAvailable && $retryCount <= $maxRetries) {
            $response = $this->client->get($link);
            $isAvailable = $this->isStatusAvailable($response['content']['status']);
            $retryCount++;
            usleep(300000);
        }

        return $response['headers']['location'][0];
    }

    /**
     * Filter the results and keep only the matching cp/dpt
     * @param array $csvBody
     * @param array $keywordPostalCodes
     * @return array
     */
    private function filterByPostalCodes(array $csvBody, array $keywordPostalCodes): array
    {
        return array_values(array_filter($csvBody, function ($csvLine) use ($keywordPostalCodes) {
            return in_array($csvLine[0], $keywordPostalCodes);
        }));
    }

    /**
     * POST & GET & loop on csv uri to retrieve forecast
     *
     * @param $query
     * @return array
     */
    public function getForecast(array $query): array
    {
        $locationHeader = $this->post($query);
        $csvUri = $this->getCsvUri($locationHeader);

        // csv body in array
        $csvBody = array_map(function ($string) {
            return str_getcsv($string, ';');
        }, file($csvUri));

        // remove the additional codes not wanted in initial request
        $csvBody = $this->filterByPostalCodes($csvBody, $query['filter'][2]['keyword']);

        return $csvBody;
    }
}
