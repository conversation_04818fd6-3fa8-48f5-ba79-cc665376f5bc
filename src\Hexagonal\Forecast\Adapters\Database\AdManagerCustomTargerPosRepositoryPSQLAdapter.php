<?php

declare(strict_types=1);

namespace App\Hexagonal\Forecast\Adapters\Database;

use App\Entity\AdManagerCustomTargetPos;
use App\Hexagonal\Forecast\Domain\Database\AdManagerCustomTargetPosRepositoryPort;
use Doctrine\Persistence\ManagerRegistry;

final class AdManagerCustomTargerPosRepositoryPSQLAdapter implements AdManagerCustomTargetPosRepositoryPort
{
    private ManagerRegistry $managerRegistry;

    public function __construct(ManagerRegistry $managerRegistry)
    {
        $this->managerRegistry = $managerRegistry;
    }

    /**
     * @inheritDoc
     */
    public function getByIds(array $ids): array
    {
        $adUnitRepository = $this->managerRegistry->getRepository(AdManagerCustomTargetPos::class);
        return $adUnitRepository->findBy(['id' => $ids]);
    }
}