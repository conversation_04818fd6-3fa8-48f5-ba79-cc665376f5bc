<?php

declare(strict_types=1);

namespace App\Hexagonal\Count\Adapters\Validator;

use App\Entity\Count;
use App\Entity\User;
use App\Hexagonal\Client\Domain\ClientRepositoryPort;
use App\Hexagonal\Count\Domain\Validator\CountValidatorPort;
use App\Hexagonal\Product\Domain\Database\ProductRepositoryPort;

class CountValidator implements CountValidatorPort
{
    private const MAX_POSTAL_CODE = 15;

    private string $errorMessage;
    private ProductRepositoryPort $productRepository;
    private ClientRepositoryPort $clientRepository;

    public function __construct(ProductRepositoryPort $productRepository, ClientRepositoryPort $clientRepository)
    {
        $this->productRepository = $productRepository;
        $this->clientRepository = $clientRepository;
        $this->errorMessage = '';
    }

    /**
     * @inheritDoc
     */
    public function getErrorMessage(): string
    {
        return $this->errorMessage;
    }

    /**
     * @inheritDoc
     */
    public function setErrorMessage(string $errorMessage): void
    {
        $this->errorMessage = $errorMessage;
    }

    /**
     * @inheritDoc
     */
    public function isPostValid(Count $count, User $user): bool
    {
        if (!$this->isProductIdValid($count->getProductId())) {
            $this->setErrorMessage('Product id is unknown.');
            return false;
        }

        if ($user->getTypeProfil() !== 'commerce') {
            $this->setErrorMessage('Your profile does not allow you to create a count.');
            return false;
        }

        if ($count->getStartDate() >= $count->getEndDate()) {
            $this->setErrorMessage('Start date must be greater than end Date.');
            return false;
        }

        if (count(array_unique($count->getProjectType())) !== count($count->getProjectType())) {
            $this->setErrorMessage('Duplicate project types.');
            return false;
        }

        if (!$this->isLocalityValid($count)) {
            return false;
        }

        if (!$this->isCountItemValid($count)) {
            return false;
        }

        // Expert360 client check
        if ($count->getProductId() === 2 && !$this->clientRepository->getAllByClientId($count->getClientId())) {
            $this->setErrorMessage('Unknown clientId.');
            return false;
        }

        return true;
    }

    /**
     * @param Count $count
     * @param User $user
     * @return bool
     */
    public function isPatchValid(Count $count, User $user): bool
    {
        if ($user->getTypeProfil() !== 'commerce') {
            $this->setErrorMessage('Your profile does not allow you to update a count.');
            return false;
        }

        if (!$this->isStatusValid($count)) {
            return false;
        }

        return true;
    }

    /**
     * Check if product exists
     *
     * @param int $productId
     * @return bool
     */
    private function isProductIdValid(int $productId): bool
    {
        $product = $this->productRepository->get($productId);

        return $product === null ? false : true;
    }

    /**
     * @param Count $count
     * @return bool
     */
    private function isLocalityValid(Count $count): bool
    {
        $locality = $count->getLocality();

        if (count($locality) > self::MAX_POSTAL_CODE) {
            $this->setErrorMessage('Maximum postal codes is ' . self::MAX_POSTAL_CODE . '.');
            return false;
        }

        foreach ($locality as $local) {
            if (!is_array($local)) {
                $this->setErrorMessage('Invalid locality format.');
                return false;
            }

            if (!array_key_exists('name', $local) || !array_key_exists('code', $local)) {
                $this->setErrorMessage('Locality : name or code is missing.');
                return false;
            }
            if (count(array_keys($local)) > 2) {
                $this->setErrorMessage('Locality : too many keys.');
                return false;
            }
        }

        return true;
    }

    /**
     * @param Count $count
     * @return bool
     */
    private function isCountItemMasterDisplayValid(Count $count): bool
    {
        $countItem = $count->getCountItem();
        if (count($countItem) > self::MAX_POSTAL_CODE) {
            $this->setErrorMessage('Maximum postal codes is ' . self::MAX_POSTAL_CODE . '.');
            return false;
        }

        $totalImpPurchased = 0;
        $totalBudget = 0;

        if (count($countItem) !== count($count->getLocality())) {
            $this->setErrorMessage('Count item and locality count must match.');
            return false;
        }

        foreach ($countItem as $item) {
            if (!is_array($item)) {
                $this->setErrorMessage('Invalid count item format.');
                return false;
            }

            if (
                !array_key_exists('name', $item)
                || !array_key_exists('code', $item)
                || !array_key_exists('sold', $item)
                || !array_key_exists('available', $item)
                || !array_key_exists('purchased', $item)
                || !array_key_exists('budget', $item)
            ) {
                $this->setErrorMessage('Keys : name or code or sold or available or purchased or budget is missing.');
                return false;
            }

            if (strlen(strval($item["code"])) !== 2 && strlen(strval($item["code"])) !== 5) {
                $this->setErrorMessage('Postal code ' . $item["code"] . ' must be 2 or 5 digits.');
                return false;
            }

            if (
                intval($item["sold"]) < 0
                || intval($item["available"]) <= 0
                || intval($item["purchased"]) <= 0
                || intval($item["budget"]) <= 0
            ) {
                $this->setErrorMessage('Fields sold or available or purchased or budget must be greater than 0.');
                return false;
            }

            if (intval($item["purchased"]) > intval($item["available"])) {
                $this->setErrorMessage('Imp purchased must be lower than imp available.');
                return false;
            }

            $totalImpPurchased += $item["purchased"];
            $totalBudget += $item["budget"];
        }

        if ($count->getTotalImpPurchased() !== $totalImpPurchased || $count->getTotalBudget() !== $totalBudget) {
            $this->setErrorMessage('The total (purchased or budget) is different from the cumulative (purchased or budget).');
            return false;
        }

        return true;
    }

    /**
     * @param Count $count
     * @return bool
     */
    private function isCountItemExpert360Valid(Count $count): bool
    {
        $countItem = $count->getCountItem()[0];

        if (
            !array_key_exists('displayBudget', $countItem)
            || !array_key_exists('retargetingBudget', $countItem)
            || !array_key_exists('intensity', $countItem)
            || !array_key_exists('prints', $countItem)
            || !array_key_exists('brandContentComment', $countItem)
        ) {
            $this->setErrorMessage('Missing keys : displayBudget, retargetingBudget, intensity, prints or brandContentComment is missing.');
            return false;
        }

        if (!is_string($countItem['brandContentComment'])) {
            $this->setErrorMessage('brandContentComment must be a string');
            return false;
        }

        if (!is_null($countItem['prints']) && !is_int($countItem['prints'])) {
            $this->setErrorMessage('prints must be an int or null');
            return false;
        }

        if (!is_int($countItem['intensity'])) {
            $this->setErrorMessage('intensity must be an int');
            return false;
        }

        if (strlen($countItem['brandContentComment']) > 800) {
            $this->setErrorMessage('brandContentComment characters count exceeded (800)');
            return false;
        }

        if (round($countItem['retargetingBudget'], 2) !== round(0.05 * $countItem['displayBudget'], 2)) {
            $this->setErrorMessage('retargetingBudget must be 0.05*displayBudget');
            return false;
        }

        if (!in_array($countItem['intensity'], [1, 2, 3])) {
            $this->setErrorMessage('intensity must be 1,2 or 3');
            return false;
        }

        if ($countItem['displayBudget'] > $count->getTotalBudget()) {
            $this->setErrorMessage('displayBudget cannot be greater than totalBudget.');
            return false;
        }

        return true;
    }

    /**
     * Check if countItem param is valid
     *
     * @param Count $count
     * @return bool
     */
    private function isCountItemValid(Count $count): bool
    {
        $productId = $count->getProductId();
        if ($productId === 1) {
            return $this->isCountItemMasterDisplayValid($count);
        } elseif ($productId === 2) {
            return $this->isCountItemExpert360Valid($count);
        }

        return true;
    }

    /**
     * @param Count $count
     * @return bool
     */
    private function isStatusValid(Count $count): bool
    {
        if ($count->getStatus() !== 'archived') {
            $this->setErrorMessage('status must be archived');
            return false;
        }

        return true;
    }
}
