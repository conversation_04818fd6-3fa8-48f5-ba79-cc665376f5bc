<?php
/**
 * Created by PhpStorm.
 * User: 924871
 * Date: 01/03/2021
 * Time: 15:44
 */

declare(strict_types=1);

namespace App\Tests\Hexagonal\Security\Adapters\JWT\TokenAuthenticator;

use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Request;
use App\Hexagonal\Security\Adapters\JWT\TokenAuthenticator\JWTTokenAuthenticator;
use App\Hexagonal\Security\Domain\JWT\TokenManager\TokenManagerPort;
use App\Hexagonal\Security\Domain\JWT\TokenExtractor\TokenExtractorPort;
use App\Hexagonal\Security\Domain\PublicKeyManager\PublicKeyManagerPort;
use App\Hexagonal\User\Domain\AuthenticateUserPort;
use Symfony\Component\Security\Core\Exception\CustomUserMessageAuthenticationException;

class JWTTokenAuthenticatorTest extends TestCase
{
    public function testSupports()
    {
        $tokenManager = $this->createMock(TokenManagerPort::class);

        $request = new Request();
        $request->headers->set('Authorization', 'Bearer aa.bb.cc');
        $tokenExtractor = $this->createMock(TokenExtractorPort::class);
        $tokenExtractor
            ->expects($this->any())
            ->method('extract')
            ->with($request)
            ->willReturn(true);

        $authenticateUser = $this->createMock(AuthenticateUserPort::class);
        $publicKeyManager = $this->createMock(PublicKeyManagerPort::class);

        $authenticator = new JWTTokenAuthenticator($tokenManager, $tokenExtractor, $authenticateUser, $publicKeyManager);
        $supportsResult = $authenticator->supports($request);

        $this->assertTrue($supportsResult);

        $request = new Request();
        $tokenExtractor = $this->createMock(TokenExtractorPort::class);
        $tokenExtractor
            ->expects($this->any())
            ->method('extract')
            ->with($request)
            ->willReturn(false);

        $authenticator = new JWTTokenAuthenticator($tokenManager, $tokenExtractor, $authenticateUser, $publicKeyManager);
        $supportsResult = $authenticator->supports($request);

        $this->assertFalse($supportsResult);
    }

    public function testAuthenticateNoJWT()
    {
        $tokenManager = $this->createMock(TokenManagerPort::class);

        $request = new Request();
        $request->headers->set('Authorization', 'Bearer aa.bb.cc');
        $tokenExtractor = $this->createMock(TokenExtractorPort::class);
        $tokenExtractor
            ->expects($this->any())
            ->method('extract')
            ->with($request)
            ->willReturn(null);

        $authenticateUser = $this->createMock(AuthenticateUserPort::class);
        $publicKeyManager = $this->createMock(PublicKeyManagerPort::class);

        $authenticator = new JWTTokenAuthenticator($tokenManager, $tokenExtractor, $authenticateUser,
            $publicKeyManager);

        try{
            $result = $authenticator->authenticate($request);
        }catch(CustomUserMessageAuthenticationException $e){
            $this->assertEquals('No API token provided', $e->getMessage());
        }
    }

    public function testAuthenticateNoKid()
    {
        $jwt = 'a.b.c';

        $tokenManager = $this->createMock(TokenManagerPort::class);
        $tokenManager
            ->expects($this->any())
            ->method('getKid')
            ->with($jwt)
            ->willReturn(null);

        $request = new Request();
        $request->headers->set('Authorization', 'Bearer aa.bb.cc');
        $tokenExtractor = $this->createMock(TokenExtractorPort::class);
        $tokenExtractor
            ->expects($this->any())
            ->method('extract')
            ->with($request)
            ->willReturn($jwt);

        $authenticateUser = $this->createMock(AuthenticateUserPort::class);
        $publicKeyManager = $this->createMock(PublicKeyManagerPort::class);

        $authenticator = new JWTTokenAuthenticator($tokenManager, $tokenExtractor, $authenticateUser,
            $publicKeyManager);

        try{
            $result = $authenticator->authenticate($request);
        }catch(CustomUserMessageAuthenticationException $e){
            $this->assertEquals('Authorization has been denied for this request', $e->getMessage());
        }
    }

    public function testAuthenticateNoFetch()
    {
        $jwt = 'a.b.c';
        $kid = 'zzzzz';

        $tokenManager = $this->createMock(TokenManagerPort::class);
        $tokenManager
            ->expects($this->any())
            ->method('getKid')
            ->with($jwt)
            ->willReturn($kid);

        $request = new Request();
        $request->headers->set('Authorization', 'Bearer aa.bb.cc');
        $tokenExtractor = $this->createMock(TokenExtractorPort::class);
        $tokenExtractor
            ->expects($this->any())
            ->method('extract')
            ->with($request)
            ->willReturn($jwt);

        $authenticateUser = $this->createMock(AuthenticateUserPort::class);
        $publicKeyManager = $this->createMock(PublicKeyManagerPort::class);
        $publicKeyManager
            ->expects($this->any())
            ->method('fetch')
            ->with($kid)
            ->willReturn(null);

        $authenticator = new JWTTokenAuthenticator($tokenManager, $tokenExtractor, $authenticateUser,
            $publicKeyManager);

        try{
            $result = $authenticator->authenticate($request);
        }catch(CustomUserMessageAuthenticationException $e){
            $this->assertEquals('Authorization has been denied for this request', $e->getMessage());
        }
    }
}
