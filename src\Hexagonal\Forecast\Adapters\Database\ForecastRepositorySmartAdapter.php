<?php

declare(strict_types=1);

namespace App\Hexagonal\Forecast\Adapters\Database;

use App\Hexagonal\Forecast\Domain\Database\ForecastRepositoryPort;
use App\Hexagonal\Forecast\Domain\Model\ForecastParams;
use App\Hexagonal\Forecast\Adapters\Database\SmartAdServer\SmartForecastService;

final class ForecastRepositorySmartAdapter implements ForecastRepositoryPort
{
    private SmartForecastService $smartForecastService;

    /**
     * @param SmartForecastService $smartForecastService
     */
    public function __construct(SmartForecastService $smartForecastService)
    {
        $this->smartForecastService = $smartForecastService;
    }

    public function get(ForecastParams $forecastParams): array
    {
        return $this->smartForecastService->getForecast($forecastParams);
    }
}