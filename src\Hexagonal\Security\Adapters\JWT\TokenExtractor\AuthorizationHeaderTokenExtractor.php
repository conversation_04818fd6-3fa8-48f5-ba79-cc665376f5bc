<?php

declare(strict_types=1);

namespace App\Hexagonal\Security\Adapters\JWT\TokenExtractor;

use Symfony\Component\HttpFoundation\Request;
use App\Hexagonal\Security\Domain\JWT\TokenExtractor\TokenExtractorPort;

final class AuthorizationHeaderTokenExtractor implements TokenExtractorPort
{
    private string $prefix;
    private string $name;

    public function __construct(?string $prefix, string $name)
    {
        $this->prefix = $prefix;
        $this->name = $name;
    }

    /**
     * {@inheritdoc}
     */
    public function extract(Request $request)
    {
        if (!$request->headers->has($this->name)) {
            return false;
        }

        $authorizationHeader = $request->headers->get($this->name);

        if (empty($this->prefix)) {
            return $authorizationHeader;
        }

        $headerParts = explode(' ', $authorizationHeader);

        if (!(2 === count($headerParts) && 0 === strcasecmp($headerParts[0], $this->prefix))) {
            return false;
        }

        return $headerParts[1];
    }
}
