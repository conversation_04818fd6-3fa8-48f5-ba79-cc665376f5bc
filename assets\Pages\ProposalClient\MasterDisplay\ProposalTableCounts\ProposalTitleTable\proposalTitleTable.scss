.titleTable {
  display: grid;
  grid-template-columns: 33.4% 33.3% 33.3%;

  .blue {
    background-color: $darkerSaphir;
    color: $white;
  }
  > div {
    display: flex;
    align-items: center;
    font-family: $font-sourceSansPro;
    font-weight: 700;
    padding: 0.5em 1.25em;
    border-top: 1px solid $lighterGrey;
    border-left: 1px solid $lighterGrey;

    &:first-child {
      border-top-left-radius: 0.3em;
    }
    &:last-child {
      border-top-right-radius: 0.3em;
    }
    &:nth-child(3n) {
      border-right: 1px solid $darkerSaphir;
      border-top: 1px solid $darkerSaphir;
    }
    &:nth-child(2n) {
      border-left: 1px solid $darkerSaphir;
      border-top: 1px solid $darkerSaphir;
    }
    p {
      span {
        font-weight: 500;
      }
    }
    .locationRow {
      display: flex;
      align-items: center;
    }
  }
}
