<?php

namespace App\Hexagonal\Forecast\Adapters\Database\GoogleAdManager;

use Google\AdsApi\AdManager\AdManagerSession;
use Google\AdsApi\Common\OAuth2TokenBuilder;
use Google\AdsApi\AdManager\AdManagerSessionBuilder;

/**
 * Session class to create AdManagerSession object
 */
class SessionService
{
    private AdManagerSession $adManagerSession;

    public function __construct($adManagerConfigPath)
    {
        $this->setAdManagerSession($adManagerConfigPath);
    }

    /**
     * Create AdManagerSession with OAuth2 Token from config file
     *
     * @param string $configPath path to adsadpi_php.ini
     */
    private function setAdManagerSession(string $configPath): void
    {
        // Generate a refreshable OAuth2 credential for authentication.
        $oAuth2Credential = (new OAuth2TokenBuilder())
            ->fromFile($configPath)
            ->build();

        // Construct an API session configured from a properties file and the OAuth2
        // credentials above.
        $this->adManagerSession = (new AdManagerSessionBuilder())
            ->fromFile($configPath)
            ->withOAuth2Credential($oAuth2Credential)
            ->build();
    }

    /**
     * Return AdManagerSession
     *
     * @return AdManagerSession
     */
    public function getAdManagerSession(): AdManagerSession
    {
        return $this->adManagerSession;
    }
}
