<?php

declare(strict_types=1);

namespace App\Hexagonal\Client\Domain;

use App\Entity\Client;

interface ClientRepositoryPort
{
    /**
     * @param string $clientId
     * @return ?Client
     */
    public function getByClientId(string $clientId): ?Client;

    /**
     * Find all matching clientId
     * @param string $clientId
     * @return array
     */
    public function getAllByClientId(string $clientId): array;

    /**
     * @param array $clients
     * @return void
     */
    public function bulkSave(array $clients): void;
}