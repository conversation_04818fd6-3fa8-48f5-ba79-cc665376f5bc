<?php

declare(strict_types=1);

namespace App\Command;

use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\{
    Console\Input\InputInterface,
    Console\Input\InputOption,
    Console\Output\OutputInterface
};

class AdFactoryDebugCommand extends Command
{
    const COMMAND_NAME = 'adfactory:debug';

    private $logger;

    public function __construct(LoggerInterface $logger)
    {
        $this->logger = $logger;

        parent::__construct();
    }

    public function configure()
    {
        $this->setName(self::COMMAND_NAME)
            ->setDescription('AdFactory Debug Command');
    }

    /**
     * @param InputInterface $input
     * @param OutputInterface $output
     *
     * @return int|null|void
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        try {
            echo "Traitement OK\n";
            return Command::SUCCESS;
        } catch (\Throwable $e) {
            $this->logger->error(
                'AdFactory Debug KO',
                [$e->getMessage(), 'File : ' . $e->getFile(), 'Line : ' . $e->getLine()]
            );
            $this->logger->error($e->getTraceAsString());
            echo "Traitement KO\n";
            return Command::FAILURE;
        }
    }
}
