<?php

declare(strict_types=1);

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass="App\Repository\ZipCityRepository")
 * @ORM\Table(name="ref_city")
 */
class ZipCity
{
    /**
     * @var int
     *
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     */
    private int $id;

    /**
     * @var string city name
     *
     * @ORM\Column(name="name", type="string")
     */
    private string $name;

    /**
     * @var string city name (light)
     *
     * @ORM\Column(name="name_light", type="string")
     */
    private string $nameLight;

    /**
     * @var string city code
     *
     * @ORM\Column(name="code", type="string")
     */
    private string $code;

    /**
     * @var string id admanager
     *
     * @ORM\Column(name="id_admanager", type="string")
     */
    private string $admanagerId;

    /**
     * @param string $name
     * @param string $code
     * @param string $admanagerId
     */
    public function __construct(string $name, string $code, string $admanagerId)
    {
        $this->name = $name;
        $this->code = $code;
        $this->admanagerId = $admanagerId;
    }

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @return string
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * @return string
     */
    public function getCode(): string
    {
        return $this->code;
    }

    /**
     * Get id admanager
     *
     * @return string
     */ 
    public function getAdmanagerId(): string
    {
        return $this->admanagerId;
    }
}
