<?php

declare(strict_types=1);

namespace App\Hexagonal\Count\Domain\Database;

use App\Entity\Count;

interface CountRepositoryPort
{
    /**
     * Create a Count
     * @param Count $count
     * @return Count
     */
    public function create(Count $count): Count;

    /**
     * Update a Count
     * @param Count $count
     * @return Count
     */
    public function update(Count $count): Count;

    /**
     * Get a Count
     * @param int $id
     * @return Count|null
     */
    public function get(int $id): ?Count;
}
