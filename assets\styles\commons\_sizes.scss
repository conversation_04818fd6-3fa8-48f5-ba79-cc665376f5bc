// Extra small screen / phone
$screen-xsCustom: 480px;
// Small screen / tablet
$screen-smCustom: 768px;
// Medium screen / desktop
$screen-mdCustom: 1024px;
// Large screen / wide desktop
$screen-lgCustom: 1280px;
// extra Large screen / extra wide desktop
$screen-xlgCustom: 1440px;

//-------------------------------------------------------------------
// Media queries mixins

@mixin smallMobile {
    @media (max-width: $screen-xsCustom) {
        @content;
    }
}
@mixin mediumMobile {
    @media (min-width: $screen-xsCustom + 1) and (max-width: $screen-smCustom) {
        @content;
    }
}
@mixin mobilePortrait {
    @media (max-width: $screen-smCustom) and (max-aspect-ratio: 16/10) {
        @content;
    }
}
@mixin mobileLandscape {
    @media (max-width: $screen-smCustom) and (min-aspect-ratio: 16/10) {
        @content;
    }
}
@mixin mobile {
    @media (max-width: $screen-smCustom) {
        @content;
    }
}
@mixin desktop {
    @media (min-width: $screen-smCustom + 1) {
        @content;
    }
}
@mixin smallDesktop {
    @media (min-width: $screen-smCustom + 1) and (max-width: $screen-lgCustom) {
        @content;
    }
}
@mixin verySmallDesktop {
    @media (min-width: $screen-smCustom + 1) and (max-width: $screen-mdCustom) {
        @content;
    }
}
@mixin middleDesktop {
    @media (max-width: $screen-lgCustom) {
        @content;
    }
}

@mixin largeDesktop {
    @media (min-width: $screen-xlgCustom) {
        @content;
    }
}
