<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210211110349 extends AbstractMigration
{
    public function getDescription() : string
    {
        return '';
    }

    public function up(Schema $schema) : void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE product_adfactory ALTER admanager_adunit TYPE TEXT');
        $this->addSql('ALTER TABLE product_adfactory ALTER smart_pages TYPE TEXT');
        $this->addSql('COMMENT ON COLUMN product_adfactory.admanager_adunit IS \'(DC2Type:array)\'');
        $this->addSql('COMMENT ON COLUMN product_adfactory.smart_pages IS \'(DC2Type:array)\'');
    }

    public function down(Schema $schema) : void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE product_adfactory ALTER admanager_adunit TYPE VARCHAR(50)');
        $this->addSql('ALTER TABLE product_adfactory ALTER smart_pages TYPE VARCHAR(50)');
        $this->addSql('COMMENT ON COLUMN product_adfactory.admanager_adunit IS NULL');
        $this->addSql('COMMENT ON COLUMN product_adfactory.smart_pages IS NULL');
    }
}
