<?php

declare(strict_types=1);

namespace App\Hexagonal\User\Adapters;

use App\Hexagonal\User\Domain\AuthenticateUserPort;
use App\Hexagonal\User\Domain\Database\UserRepositoryPort;
use Symfony\Component\Security\Core\User\UserInterface;

final class AuthenticateUser implements AuthenticateUserPort
{
    private UserRepositoryPort $userRepository;
    private const ADMIN = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
    ];

    public function __construct(UserRepositoryPort $userRepository)
    {
        $this->userRepository = $userRepository;
    }

    /**
     * @inheritdoc
     */
    public function process(UserInterface $user): UserInterface
    {
        // Find logged user
        $existingUser = $this->userRepository->getByEmail($user->getEmail());

        // User not found : create user
        $roles = in_array($user->getEmail(), self::ADMIN) ? ['ROLE_ADMIN'] : ['ROLE_USER'];
        $user->setRoles($roles);
        $existingUser ??= $this->userRepository->create($user);

        return $existingUser;
    }
}
