<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\AdManagerCustomTargetPos;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method AdManagerCustomTargetPos|null find($id, $lockMode = null, $lockVersion = null)
 * @method AdManagerCustomTargetPos|null findOneBy(array $criteria, array $orderBy = null)
 * @method AdManagerCustomTargetPos[]    findAll()
 * @method AdManagerCustomTargetPos[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class AdManagerCustomTargetPosRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, AdManagerCustomTargetPos::class);
    }

}
