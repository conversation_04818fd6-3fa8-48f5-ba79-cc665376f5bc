<?php

declare(strict_types=1);

namespace App\Hexagonal\Forecast\Adapters\Database\SmartAdServer;

final class Auth implements AuthInterface
{
    private string $smartApiUser;
    private string $smartApiPassword;

    public function __construct(string $smartApiUser, string $smartApiPassword)
    {
        $this->smartApiUser = $smartApiUser;
        $this->smartApiPassword = $smartApiPassword;
    }

    /**
     * @inheritDoc
     */
    public function getAuth(): array
    {
        return [
            'type' => 'auth_basic',
            'user' => $this->smartApiUser,
            'password' => $this->smartApiPassword,
        ];
    }
}
