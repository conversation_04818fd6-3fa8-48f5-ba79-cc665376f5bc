<?php

declare(strict_types=1);

namespace App\Hexagonal\Forecast\Adapters\Database;

use App\Entity\AdUnit;
use App\Hexagonal\Forecast\Domain\Database\AdUnitRepositoryPort;
use Doctrine\Persistence\ManagerRegistry;

final class AdUnitRepositoryPSQLAdapter implements AdUnitRepositoryPort
{
    private ManagerRegistry $managerRegistry;

    public function __construct(ManagerRegistry $managerRegistry)
    {
        $this->managerRegistry = $managerRegistry;
    }

    /**
     * @inheritDoc
     */
    public function getByIds(array $ids): array
    {
        $adUnitRepository = $this->managerRegistry->getRepository(AdUnit::class);
        return $adUnitRepository->findBy(['id' => $ids]);
    }
}