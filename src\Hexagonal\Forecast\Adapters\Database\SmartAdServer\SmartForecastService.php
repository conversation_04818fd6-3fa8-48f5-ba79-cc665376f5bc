<?php

declare(strict_types=1);

namespace App\Hexagonal\Forecast\Adapters\Database\SmartAdServer;

use App\Hexagonal\Forecast\Domain\Model\ForecastParams;

class SmartForecastService
{
    private SmartApi $api;
    private SmartOptions $smartOptions;

    public function __construct(SmartApi $api, SmartOptions $smartOptions)
    {
        $this->api = $api;
        $this->smartOptions = $smartOptions;
    }

    /**
     * @param ForecastParams $forecastParams
     * @return array
     */
    public function getForecast(ForecastParams $forecastParams): array
    {
        // clone initial params and keep only dpt type request
        $forecastParamsOnlyDpt = clone($forecastParams);
        $forecastParamsOnlyDpt->setPostalCode($this->filterPostalCodesByType($forecastParams->getPostalCode(), 'dpt'));

        $forecastParams->setPostalCode($this->filterPostalCodesByType($forecastParams->getPostalCode()));

        // split incompatible with cp and dpt in same query
        // separate query in two : one for cp, one for dpt
        $apiResultsCp = $apiResultsDpt = [];
        if(!empty($forecastParams->getPostalCode())){
            $this->smartOptions->setOptions($forecastParams);

            $queryBodyCp = $this->smartOptions->getQueryBody();
            $apiResultsCp = $this->api->getForecast($queryBodyCp);
        }
        if(!empty($forecastParamsOnlyDpt->getPostalCode())){
            $this->smartOptions->setOptions($forecastParamsOnlyDpt);

            $queryBodyDpt = $this->smartOptions->getQueryBody();
            $apiResultsDpt = $this->api->getForecast($queryBodyDpt);
        }

        $apiResults = array_merge($apiResultsCp, $apiResultsDpt);

        $forecastResults = [];
        foreach($apiResults as $apiResult){
            $matchedUnits = intval($apiResult[1]);
            $availableUnits = intval($apiResult[3]);

            // "cp=94200" or "dpt=92"
            $postalCode = explode('=', $apiResult[0])[1];

            $forecastResults[$postalCode] = [
                'matched' => $matchedUnits,
                'available' => $availableUnits,
                'sold' => $matchedUnits - $availableUnits,
            ];
        }

        return $forecastResults;
    }

    /**
     * Keep postalCodes by type (cp or dpt)
     * @param array $postalCodes
     * @param string $type
     * @return array
     */
    private function filterPostalCodesByType(array $postalCodes, string $type = 'cp'): array
    {
        return array_values(array_filter($postalCodes, function ($postalCode) use ($type) {
            if ($type === 'dpt') {
                return strlen($postalCode) === 2;
            }
            return strlen($postalCode) !== 2;
        }));
    }
}
