.tableResult {
  display: grid;
  grid-template-columns: 33.4% 33.3% 33.3%;
  grid-template-rows: 4em;

  .loadingBarsTotalPrint {
    .firstBarLoading {
      width: 4.313em;
      height: 1.375em;
      border-radius: 0.25em;
      margin-bottom: 0.25em;
      background-color: $lighterSaphir;
    }

    .secondBarLoading {
      width: 7.5em;
      height: 1em;
      border-radius: 0.25em;
      background-color: $lighterSaphir;
    }

    .firstBarLoading,
    .secondBarLoading {
      animation-duration: 1s;
      animation-fill-mode: forwards;
      animation-iteration-count: infinite;
      animation-name: lazyLoadTableCustom;
      animation-timing-function: linear;
      animation-timing-function: linear;
      background: $lighterSaphir;
      background-size: 8em 1.4em;
      background-image: linear-gradient(
        to right,
        $lighterSaphir 0%,
        $ghostWhite 20%,
        $lighterSaphir 40%,
        $lighterSaphir 100%
      );
    }
  }

  .loadingBarTotalBudget {
    width: 4.313em;
    height: 1.375em;
    border-radius: 0.25em;
    margin-bottom: 0.25em;
    animation-duration: 1s;
    animation-fill-mode: forwards;
    animation-iteration-count: infinite;
    animation-name: lazyLoadTableCustom;
    animation-timing-function: linear;
    animation-timing-function: linear;
    background: $lighterSaphir;
    background-size: 8em 1.4em;
    background-image: linear-gradient(
      to right,
      $lighterSaphir 0%,
      $ghostWhite 20%,
      $lighterSaphir 40%,
      $lighterSaphir 100%
    );
  }

  > div {
    display: flex;
    align-items: center;
    padding: 0.5em 1.25em;

    &:nth-child(1) {
      grid-column: 1;
      padding: 0.5em 1.6em;
      justify-content: flex-end;
    }
    &:nth-child(2n) {
      border-bottom-left-radius: 0.3em;
      border-left: 1px solid $darkerSaphir;
      border-bottom: 1px solid $darkerSaphir;
    }
    &:nth-child(3n) {
      border-bottom-right-radius: 0.3em;
      border-right: 1px solid $darkerSaphir;
      border-bottom: 1px solid $darkerSaphir;
      border-left: 1px solid $lighterGrey;
    }
    p {
      color: $black;
      font-family: $font-sourceSansPro;
      font-weight: 500;

      &.titleTotalCounts {
        font-weight: 700;
      }

      span {
        font-size: 0.75em;
        display: block;
      }
    }
    .totalBudget {
      display: flex;
      align-items: center;
      p {
        font-size: 1.125em;
        font-weight: 700;
      }

      span {
        font-size: 0.75em;
        color: $grey;
        margin-left: 0.75em;
      }
    }
  }
  @keyframes lazyLoadTableCustom {
    0% {
      background-position: -15em 0;
    }

    100% {
      background-position: 15em 0;
    }
  }
}
