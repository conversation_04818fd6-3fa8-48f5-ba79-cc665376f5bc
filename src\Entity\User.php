<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;
use App\Repository\UserRepository;
use ApiPlatform\Core\Annotation\ApiResource;
use Symfony\Component\Serializer\Annotation\Groups;
use Symfony\Component\Security\Core\User\UserInterface;
use Symfony\Component\Validator\Constraints as Assert;

/**
 * @ApiResource(
 *     formats={"json", "jsonld"},
 *     attributes={"pagination_client_items_per_page"=true},
 *     collectionOperations={
 *        "get"={
 *            "normalization_context"={"groups"={"usersRead"}},
 *            "security"="is_granted('ROLE_ADMIN')",
 *        },
 *     },
 *     itemOperations={
 *        "get"={
 *            "method": "GET",
 *            "path"="/users/{id}",
 *            "normalization_context"={"groups"={"usersRead"}},
 *        },
 *        "put"={
 *            "method": "PUT",
 *            "path"="/users/{id}",
 *            "normalization_context"={"groups"={"usersRead"}},
 *            "denormalization_context"={"groups"={"usersWrite"}},
 *        },
 *     },
 * )
 *
 * @ORM\Entity(repositoryClass=UserRepository::class)
 * @ORM\Table(name="users")
 */
class User implements UserInterface
{
	/**
	 * @ORM\Id()
	 * @ORM\GeneratedValue()
	 * @ORM\Column(type="integer")
	 * @Groups({"usersRead"})
	 */
	private int $id;

	/**
	 * @ORM\Column(name="email", type="string", length=255)
	 * @Groups({"usersRead"})
	 */
	private string $email;

	/**
	 * @ORM\Column(name="roles", type="array", length=255)
	 * @Groups({"usersRead"})
	 */
	private array $roles = [];

	/**
	 * @ORM\Column(name="lastname", type="string", length=255)
	 * @Groups({"usersRead"})
	 */
	private string $lastName;

	/**
	 * @ORM\Column(name="firstname", type="string", length=255)
	 * @Groups({"usersRead"})
	 */
	private string $firstName;

	/**
	 * @ORM\Column(name="type_profil", type="string", length=255, nullable=true)
	 * @Groups({"usersRead","usersWrite"})
	 * @Assert\Choice(choices = {"commerce","studio","adfactoryOperations","others"})
	 * @Assert\NotBlank()
	 */
	private ?string $typeProfil;

	/**
	 * @ORM\Column(name="market", type="array", length=255, nullable=true)
	 * @Groups({"usersRead","usersWrite"})
	 * @Assert\Choice(choices = {"ancien", "neuf", "construire", "luxe", "bureauxEtCommerces"}, multiple=true)
	 * @Assert\NotBlank()
	 */
	private ?array $market;

	public function __construct(string $email, string $firstName, string $lastName)
	{
		$this->email = $email;
		$this->firstName = $firstName;
		$this->lastName = $lastName;
	}

	/**
	 * @inheritdoc
	 */
	public function getRoles()
	{
		return $this->roles;
	}

	public function setRoles(array $roles)
	{
		$this->roles = $roles;
	}

	/**
	 * @inheritdoc
	 */
	public function getPassword()
	{
		// TODO: Implement getPassword() method.
	}

	/**
	 * @inheritdoc
	 */
	public function getSalt()
	{
		// TODO: Implement getSalt() method.
	}

	/**
	 * @inheritdoc
	 */
	public function getUsername()
	{
		return $this->email;
	}

	/**
	 * @inheritdoc
	 */
	public function eraseCredentials()
	{
		// TODO: Implement eraseCredentials() method.
	}

	/**
	 * @return string
	 */
	public function getEmail(): string
	{
		return $this->email;
	}

	/**
	 * @param string $email
	 */
	public function setEmail(string $email): void
	{
		$this->email = $email;
	}

	/**
	 * @return string
	 */
	public function getFirstName(): string
	{
		return $this->firstName;
	}

	/**
	 * @param string $firstName
	 */
	public function setFirstName(string $firstName): void
	{
		$this->firstName = $firstName;
	}

	/**
	 * @return string
	 */
	public function getLastName(): string
	{
		return $this->lastName;
	}

	/**
	 * @param string $lastName
	 */
	public function setLastName(string $lastName): void
	{
		$this->lastName = $lastName;
	}

	/**
	 * @return int
	 */
	public function getId(): int
	{
		return $this->id;
	}

	public function setId(int $id): void
	{
		$this->id = $id;
	}

	/**
	 * @return ?string
	 */
	public function getTypeProfil(): ?string
	{
		return $this->typeProfil;
	}

	public function setTypeProfil($typeProfil): void
	{
		$this->typeProfil = $typeProfil;
	}

	/**
	 * Get the value of market
	 */
	public function getMarket(): ?array
	{
		return $this->market;
	}

	/**
	 * @param array $market
	 */
	public function setMarket(array $market): void
	{
		$this->market = $market;
	}
}
