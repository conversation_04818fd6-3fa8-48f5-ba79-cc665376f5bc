<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210302160507 extends AbstractMigration
{
    public function getDescription() : string
    {
        return '';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('ALTER TABLE users DROP session_id');
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('ALTER TABLE users ADD session_id VARCHAR(255) DEFAULT NULL');
    }
}
