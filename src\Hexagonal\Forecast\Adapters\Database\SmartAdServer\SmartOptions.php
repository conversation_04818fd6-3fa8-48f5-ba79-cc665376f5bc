<?php

declare(strict_types=1);

namespace App\Hexagonal\Forecast\Adapters\Database\SmartAdServer;

use App\Hexagonal\Forecast\Domain\Database\SmartFormatRepositoryPort;
use App\Hexagonal\Forecast\Domain\Database\SmartPageRepositoryPort;
use App\Hexagonal\Forecast\Domain\Model\ForecastParams;

class SmartOptions
{
    private SmartFormatRepositoryPort $smartFormatRepository;
    private SmartPageRepositoryPort $smartPageRepository;
    private array $queryBody;

    /**
     * @param SmartFormatRepositoryPort $smartFormatRepository
     * @param SmartPageRepositoryPort $smartPageRepository
     */
    public function __construct(SmartFormatRepositoryPort $smartFormatRepository, SmartPageRepositoryPort $smartPageRepository)
    {
        $this->smartFormatRepository = $smartFormatRepository;
        $this->smartPageRepository = $smartPageRepository;
    }

    /**
     * @return array
     */
    public function getQueryBody(): array
    {
        return $this->queryBody;
    }

    /**
     * Set options (query body) for Smart call
     *
     * @param ForecastParams $forecastParams
     */
    public function setOptions(ForecastParams $forecastParams): void
    {
        $aSmartPages = [];
        $keyword = [];
        $keywordSplitType = strlen($forecastParams->getPostalCode()[0]) === 2 ? 'dpt' : 'cp';

        $pages = json_decode($forecastParams->getProduct()->getSmartPages(), true);
        foreach ($forecastParams->getProjectType() as $projectType) {
            $aSmartPages = array_merge($aSmartPages, explode(",", $pages[strtolower($projectType)]));
        }
        $aPageID = $this->getSmartPages($aSmartPages);
        $aSmartFormats = $this->getSmartFormats(explode(",", $forecastParams->getProduct()->getSmartFormats()));

        foreach($forecastParams->getPostalCode() as $postalCode){
            $keyword[] = (strlen($postalCode) === 2) ? "dpt=" . $postalCode : "cp=" . $postalCode;
        }

        $this->queryBody = [
            "startDate" => $forecastParams->getStartDate(),
            "endDate" =>  $forecastParams->getEndDate(),
            "timeZoneId" =>  "Romance Standard Time",
            "customSchedules" =>  [],
            "filter" =>  [
                [
                    "formatID" => $aSmartFormats
                ],
                [
                    "pageID" =>  $aPageID
                ],
                [
                    "keyword" => $keyword
                ]
            ],
            "fields" =>  [
                "Keyword($keywordSplitType)",
                "TotalImpressions",
                "OccupiedImpressions",
                "AvailableImpressions",
                "Revenue"
            ],
            "capping" =>  [
                "global" =>  0,
                "visit" =>  0,
                "periodic" =>  0,
                "periodInMinutes" =>  0
            ],
            "perimeterInsertionID" =>  0,
            "advancedKeywordFilter" =>  "",
            "highestPriorityGroupConsideredAsAvailable" =>  "Complement",
            "highestPriorityConsideredAsAvailable" =>  "Complement1",
            "csvSeparator" =>  ";",
            "insertionID" =>  0
        ];
    }

    /**
     * Page ID for smart 
     * Get all pages from 'smart_page' by listId
     *
     * @param array $listId
     * @return array $aPageSmartIds
     */
    private function getSmartPages(array $listId): array
    {
        $pages = $this->smartPageRepository->getByIds($listId);
        foreach ($pages as $page) {
            $aPageSmartIds[] = $page->getSmartId();
        }

        return $aPageSmartIds;
    }

    /**
     * Format ID for smart 
     * Get all formats from 'smart_format' by listId
     *
     * @param array $listId
     * @return array $aFormatSmartIds
     */
    private function getSmartFormats(array $listId): array
    {
        $formats = $this->smartFormatRepository->getByIds($listId);
        foreach ($formats as $format) {
            $aFormatSmartIds[] = $format->getSmartId();
        }

        return $aFormatSmartIds;
    }
}
