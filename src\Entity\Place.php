<?php

namespace App\Entity;

use ApiPlatform\Core\Annotation\ApiResource;
use ApiPlatform\Core\Annotation\ApiProperty;
use ApiPlatform\Core\Action\NotFoundAction;

/**
 * Returns a collection of places filtered by required "name" parameter. The parameter can be either a string or an integer.
 *
 * @ApiResource(
 *     formats={"json"},
 *     collectionOperations={"get"},
 *     itemOperations={
 *         "get"={
 *             "controller"=NotFoundAction::class,
 *             "read"=false,
 *             "output"=false,
 *         },
 *     },
 * )
 */
class Place
{
    /**
     * @var int internal id
     *
     * @ApiProperty(identifier=true)
     *
     */
    private $id;

    /**
     * @var string city/dpt code
     *
     */
    private $code;

    /**
     * @var string city/dpt name
     *
     */
    private $name;

    public function __construct(int $id, string $code, string $name)
    {
        $this->setId($id);
        $this->setCode($code);
        $this->setName($name);
    }

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @return string
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * @return string
     */
    public function getCode()
    {
        return $this->code;
    }

    /**
     * @param int $id
     */
    public function setId(int $id): void
    {
        $this->id = $id;
    }

    /**
     * @param string $name
     */
    public function setName(string $name): void
    {
        $this->name = $name;
    }

    /**
     * @param string $code
     */
    public function setCode(string $code): void
    {
        $this->code = $code;
    }
}
