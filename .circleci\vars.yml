global:
  aws_application: myadfactory
  aws_team: seloger
  aws_secret_manager_enabled: true
  iamtech_groupe_name: myadfactory
  k8s_pod_role: web
  k8s_deployment_health_liveness: /bff/api/healthcheck/liveness
  k8s_deployment_health_readiness: /bff/api/healthcheck/readiness
  k8s_deployment_health_delay: 5
  k8s_deployment_health_period: 30
  k8s_deployment_health_timeout: 5
  k8s_config_map: true
  k8s_secret_directory: /var/www
  k8s_ingress_scheme: internet-facing
  k8s_container_uid: 82
  k8s_container_port: 8080
  provider: aws
#  slack_webhook: *******************************************************************************
staging:
  k8s_lifecycle_excluded: stage
  k8s_deployment_requests_cpu: 300m
  k8s_deployment_requests_memory: 500Mi
  k8s_deployment_limits_cpu: 600m
  k8s_deployment_limits_memory: 1024Mi
  cluster_name: eks-gatsu-01
  aws_fqdn: eks.dignp.com
  aws_fqdn_acm: 2570b133-d845-4aeb-8faa-0d03a77ea409
  aws_secret_name: myadfactory-staging
  environment_vars:
    APP_ENV: staging
    BASE_URL: myadfactory-{circle_branch}.dignp.com/bff
    FRONT_URL: myadfactory-{circle_branch}.dignp.com
    DOMAIN: dignp.com
    RDS_SCHEMA: {rds_schema}
production:
  k8s_deployment_requests_cpu: 300m
  k8s_deployment_requests_memory: 500Mi
  k8s_deployment_limits_cpu: 600m
  k8s_deployment_limits_memory: 1024Mi
  cluster_name: eks-kakarotto-01
  aws_fqdn: svc.groupe-seloger.com
  aws_fqdn_acm: 786abfd8-11da-40eb-b99f-078eb7c8c288
  aws_secret_name: myadfactory-prod
  environment_vars:
    APP_ENV: prod
    BASE_URL: myadfactory.seloger.com/bff
    FRONT_URL: myadfactory.seloger.com
    DOMAIN: seloger.com