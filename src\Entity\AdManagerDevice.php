<?php

namespace App\Entity;

use App\Repository\AdManagerDeviceRepository;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass=AdManagerDeviceRepository::class)
 * @ORM\Table(name="admanager_device")
 */
class AdManagerDevice
{
    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\Column(name="label",type="string", length=255, nullable=true)
     */
    private $label;

    /**
     * @ORM\Column(name="id_admanager", type="integer")
     */
    private $admanagerId;

    /**
     * @ORM\Column(name="logicimmo_id", type="integer")
     */
    private $logicimmoId;

    public function getId(): ?int
    {
        return $this->id;
    }


    /**
     * Get the value of label
     */
    public function getLabel()
    {
        return $this->label;
    }

    /**
     * Get the value of admanagerId
     */
    public function getAdmanagerId()
    {
        return $this->admanagerId;
    }

    /**
     * Get the value of logicimmoId
     */
    public function getLogicimmoId()
    {
        return $this->logicimmoId;
    }
}
