#!/bin/sh

# retrieve admanager_key value
admanager_key=$(cat /var/www/secret.json | jq '.admanager_key')
# replace ADMANAGER_KEY by admanager_key value
sed -i "s@\"INSERT_ADMANAGER_KEY\"@${admanager_key}@" /var/www/config/admanager_api_key.json
# replace |n with \n
sed -i 's/\(|n\)/\\n/g' /var/www/config/admanager_api_key.json

# retrieve rds password/username/endpoint
rds_password=$(cat /var/www/secret.json | jq '.rds_password' | cut -d\" -f2)
rds_username=$(cat /var/www/secret.json | jq '.rds_username' | cut -d\" -f2)
rds_endpoint=$(cat /var/www/secret.json | jq '.rds_endpoint' | cut -d\" -f2)

# substitute DATABASE_URL by the rds values
sed -i "s@INSERT_RDS_PASSWORD@$rds_password@" /var/www/.env
sed -i "s@INSERT_RDS_USERNAME@$rds_username@" /var/www/.env
sed -i "s@INSERT_RDS_ENDPOINT@$rds_endpoint@" /var/www/.env

# retrieve smart username/password, then append to .env
smart_username=$(cat /var/www/secret.json | jq '.smart_username')
smart_password=$(cat /var/www/secret.json | jq '.smart_password')

echo "SMART_USER="$smart_username >> /var/www/.env
echo "SMART_PASSWORD="$smart_password >> /var/www/.env

exec "$@"
