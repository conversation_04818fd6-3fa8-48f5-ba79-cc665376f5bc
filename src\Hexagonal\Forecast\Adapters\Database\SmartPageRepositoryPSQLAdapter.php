<?php

declare(strict_types=1);

namespace App\Hexagonal\Forecast\Adapters\Database;

use App\Entity\SmartPage;
use App\Hexagonal\Forecast\Domain\Database\SmartPageRepositoryPort;
use Doctrine\Persistence\ManagerRegistry;

final class SmartPageRepositoryPSQLAdapter implements SmartPageRepositoryPort
{
    private ManagerRegistry $managerRegistry;

    public function __construct(ManagerRegistry $managerRegistry)
    {
        $this->managerRegistry = $managerRegistry;
    }

    /**
     * @inheritDoc
     */
    public function getByIds(array $ids): array
    {
        $pageRepository = $this->managerRegistry->getRepository(SmartPage::class);
        return $pageRepository->findBy(['id' => $ids]);
    }
}