<?php

namespace App\DataPersister;

use ApiPlatform\Core\DataPersister\ContextAwareDataPersisterInterface;
use App\Entity\User;
use Doctrine\Persistence\ManagerRegistry;
use ApiPlatform\Core\Exception\RuntimeException;
use Symfony\Component\HttpFoundation\RequestStack;
use App\Hexagonal\User\Domain\Database\UserRepositoryPort;

final class UserDataPersister implements ContextAwareDataPersisterInterface
{
    private UserRepositoryPort $userRepository;
    private RequestStack $requestStack;
    private const MARKET = ["ancien", "neuf", "construire", "luxe", "bureauxEtCommerces"];

    public function __construct(RequestStack $requestStack, UserRepositoryPort $userRepository)
    {
        $this->requestStack = $requestStack;
        $this->userRepository = $userRepository;
    }

    public function supports($data, array $context = []): bool
    {
        return $data instanceof User;
    }

    public function persist($user, array $context = [])
    {
        // role verification is done by UserItemDataProvider

        if ($user->getTypeProfil() !== 'commerce') {
            $user->setMarket(self::MARKET);
        } else {
            $user->setMarket(array_unique($user->getMarket()));
        }

        $this->userRepository->update($user);
    }

    public function remove($data, array $context = [])
    {
        // call your persistence layer to delete $data
    }
}
