<?php

declare(strict_types=1);

namespace App\Hexagonal\Places\Adapters\Database;

use App\Entity\Department;
use App\Entity\ZipCity;
use App\Hexagonal\Places\Domain\PlaceRepositoryPort;
use Doctrine\Persistence\ManagerRegistry;

final class PlaceRepositoryPSQLAdapter implements PlaceRepositoryPort
{
    private ManagerRegistry $managerRegistry;

    /**
     * @param ManagerRegistry $managerRegistry
     */
    public function __construct(ManagerRegistry $managerRegistry)
    {
        $this->managerRegistry = $managerRegistry;
    }

    /**
     * @param array $codes
     * @return array
     */
    public function getByCodes(array $codes): array
    {
        $dptCodes = $this->managerRegistry->getRepository(ZipCity::class)->getByCodes($codes);
        $cityCodes = $this->managerRegistry->getRepository(Department::class)->getByCodes($codes);

        return $dptCodes + $cityCodes;
    }
}