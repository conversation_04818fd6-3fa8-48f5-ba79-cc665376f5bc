<?php

namespace App\Repository;

use App\Entity\AdManagerDevice;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method AdManagerDevice|null find($id, $lockMode = null, $lockVersion = null)
 * @method AdManagerDevice|null findOneBy(array $criteria, array $orderBy = null)
 * @method AdManagerDevice[]    findAll()
 * @method AdManagerDevice[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class AdManagerDeviceRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, AdManagerDevice::class);
    }

}
