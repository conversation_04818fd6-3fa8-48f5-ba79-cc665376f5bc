<svg width="254" height="254" viewBox="0 0 254 254" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect opacity="0.16" width="254" height="254" rx="4" fill="url(#paint0_linear)"/>
<path d="M120 100.5L82 85.5M84 70L158.5 48M172 55.5L192.5 97M193.5 115L196.5 158M196.5 173L148 199.5M115.5 199.5L47.5 156.5M67.5 98.5L49 142.5M130 163.5L131.5 186M136 103L172 105M56 149L123.5 111M134 111L189.5 162M131.5 95.5L162.5 55.5M78 93.5L123.5 186M178.5 115L139.5 184.5" stroke="url(#paint1_linear)" stroke-opacity="0.4"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M166.677 153.392C165.717 149.209 162.893 145.733 159.054 144.007C156.286 141.126 145.597 138.551 140.273 135.67C136.98 133.882 134.929 130.367 134.95 126.547C135.085 124.836 135.901 123.257 137.207 122.182C140.82 117.138 142.625 110.971 142.318 104.722C142.318 92.4999 135.206 87 127.072 87C118.938 87 111.826 92.4563 111.826 104.678C111.423 110.932 113.221 117.127 116.894 122.138C118.124 123.282 118.935 124.822 119.194 126.503C119.229 130.361 117.158 133.916 113.828 135.714C108.505 138.595 97.8155 141.17 95.0474 144.051C91.1626 145.69 88.3125 149.183 87.4246 153.392C86.8585 156.669 86.8585 160.023 87.4246 163.301C98.5821 168.233 155.562 168.233 166.677 163.301C167.108 160.013 167.108 156.68 166.677 153.392Z" fill="url(#paint2_linear)"/>
<g filter="url(#filter0_d)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M172.977 40H157.023C156.458 40 156 40.4582 156 41.0234V56.9766C156 57.248 156.108 57.5083 156.3 57.7002C156.492 57.8921 156.752 58 157.023 58H165.631V50.9798H163.298V48.2635H165.622V46.2837C165.528 45.3221 165.865 44.3683 166.543 43.6796C167.221 42.9908 168.169 42.638 169.132 42.7163C169.832 42.7002 170.532 42.7386 171.226 42.831V45.2603H169.782C168.654 45.2603 168.424 45.7768 168.424 46.5707V48.3209H171.083L170.739 50.9798H168.395V57.9426H172.977C173.249 57.9451 173.511 57.8382 173.703 57.6457C173.896 57.4532 174.003 57.1914 174 56.9192V40.966C173.936 40.4412 173.504 40.0381 172.977 40.0096" fill="url(#paint3_linear)"/>
</g>
<g filter="url(#filter1_d)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M53.7672 146.906V147.223C53.7573 147.885 53.6709 148.545 53.5098 149.188C53.2969 149.864 52.9955 150.509 52.6137 151.105C52.2392 151.72 51.7543 152.26 51.1837 152.697C50.5924 153.182 49.9123 153.547 49.1817 153.77C48.4033 154.044 47.585 154.187 46.7602 154.192C45.4301 154.198 44.1276 153.811 43.0136 153.08C43.2234 153.08 43.3854 153.08 43.5952 153.08C44.7026 153.075 45.7778 152.705 46.6554 152.026C46.1392 152.028 45.6366 151.86 45.2254 151.546C44.8242 151.236 44.5306 150.807 44.3864 150.319C44.5418 150.361 44.7023 150.38 44.8631 150.377C45.0567 150.36 45.2482 150.324 45.4351 150.271C44.8707 150.179 44.3625 149.874 44.0146 149.418C43.6352 148.976 43.4285 148.41 43.4331 147.827C43.7899 148.026 44.1884 148.137 44.5962 148.152C43.9359 147.71 43.5388 146.966 43.538 146.168C43.536 145.739 43.6443 145.316 43.8526 144.941C44.4796 145.7 45.2594 146.318 46.1406 146.753C47.0281 147.208 48.0048 147.46 49.0006 147.491C48.9564 147.299 48.9403 147.102 48.9529 146.906C48.9327 146.245 49.1885 145.605 49.6584 145.142C50.1133 144.663 50.744 144.393 51.4029 144.394C52.0885 144.377 52.7458 144.669 53.1952 145.19C53.7465 145.068 54.2789 144.871 54.7777 144.605C54.5846 145.188 54.193 145.684 53.6719 146.005C54.1674 145.955 54.6492 145.812 55.0923 145.583C54.6889 146.061 54.2456 146.504 53.7672 146.906ZM56.9895 140.962C56.3731 140.313 55.5075 139.963 54.6157 140.004H43.3854C42.4929 139.959 41.6258 140.31 41.0117 140.962C40.3457 141.573 39.9768 142.444 40.0011 143.35V154.604C39.9768 155.51 40.3457 156.381 41.0117 156.991C41.6171 157.662 42.4854 158.03 43.3854 157.998H54.6157C55.5157 158.03 56.384 157.662 56.9895 156.991C57.6374 156.369 58.0027 155.506 58 154.604V143.35C57.9804 142.443 57.5991 141.583 56.9418 140.962" fill="url(#paint4_linear)"/>
</g>
<g filter="url(#filter2_d)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M205.247 156.001H188.753C188.55 155.989 188.351 156.064 188.208 156.208C188.064 156.351 187.989 156.55 188.001 156.753V173.247C187.989 173.45 188.064 173.649 188.208 173.792C188.351 173.936 188.55 174.011 188.753 173.999H205.247C205.45 174.011 205.649 173.936 205.792 173.792C205.936 173.649 206.011 173.45 205.999 173.247V156.753C206.011 156.55 205.936 156.351 205.792 156.208C205.649 156.064 205.45 155.989 205.247 156.001ZM193.328 171.368H190.697V162.777H193.403V171.368H193.328ZM191.975 161.553C191.336 161.553 190.76 161.168 190.516 160.579C190.272 159.989 190.407 159.31 190.858 158.858C191.31 158.407 191.989 158.272 192.579 158.516C193.168 158.76 193.553 159.336 193.553 159.975C193.562 160.396 193.398 160.803 193.1 161.1C192.803 161.398 192.396 161.562 191.975 161.553ZM203.368 171.368H200.672V167.148C200.672 166.17 200.672 164.893 199.319 164.893C197.966 164.893 197.752 165.966 197.752 167.04V171.336H195.046V162.745H197.601V163.926C198.134 163.041 199.114 162.524 200.146 162.584C202.852 162.584 203.368 164.377 203.368 166.707V171.368Z" fill="url(#paint5_linear)"/>
</g>
<g filter="url(#filter3_d)">
<rect x="170" y="93" width="44" height="24" rx="2" fill="white"/>
<rect x="170" y="93" width="44" height="24" rx="2" fill="url(#paint6_linear)" fill-opacity="0.72"/>
<rect x="196.654" y="98.647" width="14.8077" height="14.5083" rx="1" fill="url(#paint7_linear)"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M172.538 101.51C172.538 100.708 173.188 100.059 173.989 100.059H188.856C189.658 100.059 190.307 100.708 190.307 101.51C190.307 102.311 189.658 102.961 188.856 102.961H173.989C173.188 102.961 172.538 102.311 172.538 101.51ZM172.538 106.069C172.538 105.497 173.002 105.033 173.574 105.033H193.924C194.497 105.033 194.961 105.497 194.961 106.069C194.961 106.642 194.497 107.106 193.924 107.106H173.574C173.002 107.106 172.538 106.642 172.538 106.069ZM173.574 109.179C173.002 109.179 172.538 109.642 172.538 110.215C172.538 110.787 173.002 111.251 173.574 111.251H193.924C194.497 111.251 194.961 110.787 194.961 110.215C194.961 109.642 194.497 109.179 193.924 109.179H173.574Z" fill="white" fill-opacity="0.6"/>
<ellipse opacity="0.48" cx="173.807" cy="95.9016" rx="0.846154" ry="0.829044" fill="white" fill-opacity="0.6"/>
<ellipse opacity="0.48" cx="176.346" cy="95.9016" rx="0.846154" ry="0.829044" fill="white" fill-opacity="0.6"/>
<ellipse opacity="0.48" cx="178.884" cy="95.9016" rx="0.846154" ry="0.829044" fill="white" fill-opacity="0.6"/>
</g>
<g filter="url(#filter4_d)">
<rect x="114" y="182" width="36" height="32" rx="2" fill="white"/>
<rect x="114" y="182" width="36" height="32" rx="2" fill="url(#paint8_linear)" fill-opacity="0.72"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M117.537 187.926C116.882 187.926 116.352 188.457 116.352 189.111C116.352 189.766 116.882 190.296 117.537 190.296H141.035C141.689 190.296 142.22 189.766 142.22 189.111C142.22 188.457 141.689 187.926 141.035 187.926H117.537ZM117.222 202.148C116.741 202.148 116.352 202.538 116.352 203.018C116.352 203.499 116.741 203.888 117.222 203.888H133.828C134.308 203.888 134.698 203.499 134.698 203.018C134.698 202.538 134.308 202.148 133.828 202.148H117.222ZM116.352 206.498C116.352 206.018 116.741 205.628 117.222 205.628H133.828C134.308 205.628 134.698 206.018 134.698 206.498C134.698 206.979 134.308 207.368 133.828 207.368H117.222C116.741 207.368 116.352 206.979 116.352 206.498ZM117.222 209.108C116.741 209.108 116.352 209.498 116.352 209.978C116.352 210.459 116.741 210.848 117.222 210.848H133.828C134.308 210.848 134.698 210.459 134.698 209.978C134.698 209.498 134.308 209.108 133.828 209.108H117.222Z" fill="white" fill-opacity="0.6"/>
<rect x="116.352" y="191.482" width="31.7473" height="9.48148" rx="1" fill="url(#paint9_linear)"/>
<ellipse opacity="0.48" cx="117.115" cy="184.436" rx="0.692308" ry="0.695988" fill="white" fill-opacity="0.6"/>
<ellipse opacity="0.48" cx="119.193" cy="184.436" rx="0.692308" ry="0.695988" fill="white" fill-opacity="0.6"/>
<ellipse opacity="0.48" cx="121.269" cy="184.436" rx="0.692308" ry="0.695988" fill="white" fill-opacity="0.6"/>
</g>
<g filter="url(#filter5_d)">
<rect x="48" y="48" width="40" height="52" rx="2" fill="white"/>
<rect x="48" y="48" width="40" height="52" rx="2" fill="url(#paint10_linear)" fill-opacity="0.72"/>
<rect x="72.8228" y="59.7" width="13.0647" height="20.8" rx="1" fill="url(#paint11_linear)"/>
<path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M51.6132 54.5001C51.0609 54.5001 50.6132 54.9478 50.6132 55.5001V57.4001C50.6132 57.9523 51.0609 58.4001 51.6132 58.4001H84.8879C85.4402 58.4001 85.8879 57.9523 85.8879 57.4001V55.5001C85.8879 54.9478 85.4402 54.5001 84.8879 54.5001H51.6132ZM51.9488 59.7001C51.2109 59.7001 50.6128 60.2982 50.6128 61.036C50.6128 61.7739 51.2109 62.372 51.9488 62.372H65.4307C66.1685 62.372 66.7666 61.7739 66.7666 61.036C66.7666 60.2982 66.1685 59.7001 65.4307 59.7001H51.9488ZM50.6128 87.0361C50.6128 86.2982 51.2109 85.7001 51.9488 85.7001H65.4307C66.1685 85.7001 66.7666 86.2982 66.7666 87.0361C66.7666 87.7739 66.1685 88.372 65.4307 88.372H51.9488C51.2109 88.372 50.6128 87.7739 50.6128 87.0361ZM51.5671 72.7C51.04 72.7 50.6128 73.1273 50.6128 73.6543C50.6128 74.1813 51.04 74.6086 51.5671 74.6086H70.0431C70.5702 74.6086 70.9974 74.1813 70.9974 73.6543C70.9974 73.1273 70.5702 72.7 70.0431 72.7H51.5671ZM50.6128 65.7715C50.6128 65.2445 51.04 64.8172 51.5671 64.8172H70.0431C70.5702 64.8172 70.9974 65.2445 70.9974 65.7715C70.9974 66.2985 70.5702 66.7258 70.0431 66.7258H51.5671C51.04 66.7258 50.6128 66.2985 50.6128 65.7715ZM51.5671 90.8172C51.04 90.8172 50.6128 91.2444 50.6128 91.7715C50.6128 92.2985 51.04 92.7257 51.5671 92.7257H70.0431C70.5702 92.7257 70.9974 92.2985 70.9974 91.7715C70.9974 91.2444 70.5702 90.8172 70.0431 90.8172H51.5671ZM50.6128 77.5543C50.6128 77.0273 51.04 76.6001 51.5671 76.6001H70.0431C70.5702 76.6001 70.9974 77.0273 70.9974 77.5543C70.9974 78.0814 70.5702 78.5086 70.0431 78.5086H51.5671C51.04 78.5086 50.6128 78.0814 50.6128 77.5543ZM51.5671 68.6342C51.04 68.6342 50.6128 69.0615 50.6128 69.5885C50.6128 70.1155 51.04 70.5428 51.5671 70.5428H70.0431C70.5702 70.5428 70.9974 70.1155 70.9974 69.5885C70.9974 69.0615 70.5702 68.6342 70.0431 68.6342H51.5671ZM50.6128 95.5885C50.6128 95.0615 51.04 94.6343 51.5671 94.6343H70.0431C70.5702 94.6343 70.9974 95.0615 70.9974 95.5885C70.9974 96.1155 70.5702 96.5428 70.0431 96.5428H51.5671C51.04 96.5428 50.6128 96.1155 50.6128 95.5885ZM51.5671 80.5C51.04 80.5 50.6128 80.9273 50.6128 81.4543C50.6128 81.9813 51.04 82.4086 51.5671 82.4086H70.0431C70.5702 82.4086 70.9974 81.9813 70.9974 81.4543C70.9974 80.9273 70.5702 80.5 70.0431 80.5H51.5671Z" fill="white" fill-opacity="0.6"/>
<ellipse opacity="0.48" cx="51.4611" cy="50.6719" rx="0.769231" ry="0.763412" fill="white" fill-opacity="0.6"/>
<ellipse opacity="0.48" cx="53.7692" cy="50.6719" rx="0.769231" ry="0.763412" fill="white" fill-opacity="0.6"/>
<ellipse opacity="0.48" cx="56.0768" cy="50.6719" rx="0.769231" ry="0.763412" fill="white" fill-opacity="0.6"/>
</g>
<defs>
<filter id="filter0_d" x="148" y="34" width="34" height="34" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="4"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.16 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<filter id="filter1_d" x="32" y="134" width="34" height="34" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="4"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.16 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<filter id="filter2_d" x="180" y="150" width="34" height="34" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="4"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.16 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<filter id="filter3_d" x="146" y="75" width="92" height="72" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="6"/>
<feGaussianBlur stdDeviation="12"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.16 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<filter id="filter4_d" x="90" y="164" width="84" height="80" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="6"/>
<feGaussianBlur stdDeviation="12"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.16 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<filter id="filter5_d" x="24" y="30" width="88" height="100" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="6"/>
<feGaussianBlur stdDeviation="12"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.16 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<linearGradient id="paint0_linear" x1="363.003" y1="-109" x2="-96.9995" y2="351.003" gradientUnits="userSpaceOnUse">
<stop stop-color="#09A3E9"/>
<stop offset="1" stop-color="#6F2ACF"/>
</linearGradient>
<linearGradient id="paint1_linear" x1="196.5" y1="48.0002" x2="45.0212" y2="196.979" gradientUnits="userSpaceOnUse">
<stop stop-color="#09A3E9"/>
<stop offset="1" stop-color="#6F2ACF"/>
</linearGradient>
<linearGradient id="paint2_linear" x1="167" y1="87.0001" x2="87.0001" y2="167" gradientUnits="userSpaceOnUse">
<stop stop-color="#09A3E9"/>
<stop offset="1" stop-color="#6F2ACF"/>
</linearGradient>
<linearGradient id="paint3_linear" x1="174" y1="40" x2="156" y2="58" gradientUnits="userSpaceOnUse">
<stop stop-color="#09A3E9"/>
<stop offset="1" stop-color="#6F2ACF"/>
</linearGradient>
<linearGradient id="paint4_linear" x1="58" y1="140" x2="40" y2="158" gradientUnits="userSpaceOnUse">
<stop stop-color="#09A3E9"/>
<stop offset="1" stop-color="#6F2ACF"/>
</linearGradient>
<linearGradient id="paint5_linear" x1="206" y1="156" x2="188" y2="174" gradientUnits="userSpaceOnUse">
<stop stop-color="#09A3E9"/>
<stop offset="1" stop-color="#6F2ACF"/>
</linearGradient>
<linearGradient id="paint6_linear" x1="214" y1="93" x2="193.822" y2="129.994" gradientUnits="userSpaceOnUse">
<stop stop-color="#09A3E9"/>
<stop offset="1" stop-color="#6F2ACF"/>
</linearGradient>
<linearGradient id="paint7_linear" x1="211.462" y1="98.6471" x2="196.957" y2="113.452" gradientUnits="userSpaceOnUse">
<stop stop-color="#09A3E9"/>
<stop offset="1" stop-color="#6F2ACF"/>
</linearGradient>
<linearGradient id="paint8_linear" x1="150" y1="182" x2="118.221" y2="217.752" gradientUnits="userSpaceOnUse">
<stop stop-color="#09A3E9"/>
<stop offset="1" stop-color="#6F2ACF"/>
</linearGradient>
<linearGradient id="paint9_linear" x1="148.099" y1="191.482" x2="142.899" y2="208.892" gradientUnits="userSpaceOnUse">
<stop stop-color="#09A3E9"/>
<stop offset="1" stop-color="#6F2ACF"/>
</linearGradient>
<linearGradient id="paint10_linear" x1="88.0001" y1="48.0001" x2="37.7399" y2="86.6617" gradientUnits="userSpaceOnUse">
<stop stop-color="#09A3E9"/>
<stop offset="1" stop-color="#6F2ACF"/>
</linearGradient>
<linearGradient id="paint11_linear" x1="85.8875" y1="59.7" x2="67.1503" y2="71.469" gradientUnits="userSpaceOnUse">
<stop stop-color="#09A3E9"/>
<stop offset="1" stop-color="#6F2ACF"/>
</linearGradient>
</defs>
</svg>
