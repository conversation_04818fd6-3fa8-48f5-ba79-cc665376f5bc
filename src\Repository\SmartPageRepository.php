<?php

namespace App\Repository;

use App\Entity\SmartPage;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method SmartPage|null find($id, $lockMode = null, $lockVersion = null)
 * @method SmartPage|null findOneBy(array $criteria, array $orderBy = null)
 * @method SmartPage[]    findAll()
 * @method SmartPage[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class SmartPageRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, SmartPage::class);
    }

}
