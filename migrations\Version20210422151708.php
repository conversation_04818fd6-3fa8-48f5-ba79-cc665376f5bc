<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210422151708 extends AbstractMigration
{
    public function getDescription() : string
    {
        return '';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('CREATE SEQUENCE clients_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE TABLE clients (id INT NOT NULL, client_id VARCHAR(255) NOT NULL, intensity JSON NOT NULL, PRIMARY KEY(id))');
        $this->addSql('COMMENT ON COLUMN clients.intensity IS \'(DC2Type:json_array)\'');
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('DROP SEQUENCE clients_id_seq CASCADE');
        $this->addSql('DROP TABLE clients');
    }
}
