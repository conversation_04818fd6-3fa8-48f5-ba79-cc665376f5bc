<?php

namespace App\Tests\Hexagonal\User\Adapters\Database;

use PHPUnit\Framework\TestCase;
use App\Hexagonal\User\Adapters\Database\UserRepositoryInMemoryAdapter;
use App\Hexagonal\User\Domain\Database\UserRepositoryPort;
use App\Entity\User;

class UserRepositoryInMemoryAdapterTest extends TestCase
{
    private ?UserRepositoryPort $userRepository;

    protected function setUp()
    {
        $this->userRepository = new UserRepositoryInMemoryAdapter();
    }

    protected function tearDown()
    {
        $this->userRepository = null;
    }

    public function testCreate()
    {
        // arrange
        $user = new User(null, '<EMAIL>', 'firstNameUser4', 'nameUser4');
        $expected = 4;

        // act
        $createdUser = $this->userRepository->create($user);

        // assert
        $this->assertEquals($expected, $createdUser->getId());
    }

    public function testGetByEmail()
    {
        // arrange
        $expected = '<EMAIL>';

        // act
        $user = $this->userRepository->getByEmail($expected);

        // assert
        $this->assertEquals($expected, $user->getEmail());
    }

    public function testGet()
    {
        // arrange
        $expected = 3;

        // act
        $user = $this->userRepository->get($expected);

        // assert
        $this->assertEquals($expected, $user->getId());
    }

    public function testGetAll()
    {
        // arrange
        $expected = 3;

        // act
        $users = $this->userRepository->getAll();

        // assert
        $this->assertCount($expected, $users);
    }

    public function testUpdate()
    {
        // arrange
        $user = new User(3, '<EMAIL>', 'Gon3', 'Freecs3');
        $user->setMarket(['ancien']);
        $expected = ['ancien'];

        // act
        $updatedUser = $this->userRepository->update($user);

        // assert
        $this->assertEquals($expected, $updatedUser->getMarket());
    }
}
