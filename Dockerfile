FROM 936992537267.dkr.ecr.eu-west-1.amazonaws.com/groupeseloger/sl.alpine-node.14-runtime:latest as node
FROM 936992537267.dkr.ecr.eu-west-1.amazonaws.com/seloger/sl.alpine-php7-fpm-runtime:latest

# Copy npm & node to final image
COPY --from=node /usr/local/lib/node_modules /usr/local/lib/node_modules
COPY --from=node /usr/local/bin/node /usr/local/bin/node
RUN ln -s /usr/local/lib/node_modules/npm/bin/npm-cli.js /usr/local/bin/npm

# Env variables
ENV USER www-data
ENV PORT 8080
ENV COMPOSER_MEMORY_LIMIT=-1
ENV WORKDIR /var/www

WORKDIR $WORKDIR

# Install libs
RUN apk --update --no-cache add \
    nginx \
    supervisor \
    postgresql-dev \
    libxml2-dev \
    jq \
# Installs latest Chromium (89) package
    chromium \
    nss \
    freetype \
    freetype-dev \
    harfbuzz \
    ca-certificates \
    ttf-freefont

# Tell Puppeteer to skip installing Chrome. We'll be using the installed package.
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true \
    PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser

# Install php extensions
RUN docker-php-ext-install pdo_pgsql soap opcache sockets intl

# Install composer executable
COPY --from=composer:2 /usr/bin/composer /usr/bin/composer

# Composer install
COPY composer.json composer.lock ./
RUN composer install --no-dev

# npm install
COPY package.json package-lock.json ./
RUN npm install

# Configure nginx
COPY docker/nginx.conf /etc/nginx/nginx.conf

# Remove default server definition
RUN rm /etc/nginx/conf.d/default.conf

# Configure php
COPY docker/php.ini /usr/local/etc/php/conf.d

# Configure supervisord
COPY docker/supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# Copy app files
COPY . .

# Add permissions
RUN chown -R $USER.$USER /run && \
  chown -R $USER.$USER /var/lib/nginx && \
  chown -R $USER.$USER /var/log/nginx && \
  chown -R $USER.$USER . && \
  chmod +x $WORKDIR/docker/docker-entrypoint.sh

# Runtime script (export secret var)
ENTRYPOINT ["/var/www/docker/docker-entrypoint.sh"]

# Switch user
USER $USER

# Expose port
EXPOSE $PORT

# Start nginx & php-fpm
CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"]
