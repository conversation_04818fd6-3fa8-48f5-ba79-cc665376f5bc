<?php

declare(strict_types=1);

namespace App\Hexagonal\Forecast\Adapters;

class ForecastApiRequest implements ForecastApiRequestValidableInterface
{
    private ?int $productId;
    private ?array $postalCode;
    private ?array $broadcastMedium;
    private ?array $projectType;
    private string $startDate;
    private string $endDate;
    private ForecastApiRequestValidator $forecastApiRequestValidator;

    public function __construct(ForecastApiRequestValidator $forecastApiRequestValidator)
    {
        $this->forecastApiRequestValidator = $forecastApiRequestValidator;
    }

    public function setRequest(array $requestParameters): void
    {
        $this->postalCode = isset($requestParameters['postalCode']) ? explode(',', $requestParameters['postalCode']) : null;
        $this->productId = isset($requestParameters['productId']) ? intval($requestParameters['productId']) : null;

        // default startDate : now + 5 min
        $this->startDate = date('Y-m-d H:i:s', strtotime('+5 min'));
        // default endDate : +30d
        $this->endDate = date('Y-m-d H:i:s', strtotime('+1 month - 1 day'));

        $this->broadcastMedium = isset($requestParameters['broadcastMedium']) ? array_map('strtolower',
            explode(',',
                $requestParameters['broadcastMedium'])) : null;
        $this->projectType = isset($requestParameters['projectType']) ? array_map('strtolower',
            explode(',', $requestParameters['projectType'])
        ) : null;
    }

    /**
     * @inheritDoc
     */
    public function isValid(): bool
    {
        return $this->forecastApiRequestValidator->validate($this);
    }

    /**
     * @inheritDoc
     */
    public function getErrors(): string
    {
        return $this->forecastApiRequestValidator->getErrors();
    }

    /**
     * @return ?int
     */
    public function getProductId(): ?int
    {
        return $this->productId;
    }

    /**
     * @return ?array
     */
    public function getPostalCode(): ?array
    {
        return $this->postalCode;
    }

    /**
     * @return ?array
     */
    public function getBroadcastMedium(): ?array
    {
        return $this->broadcastMedium;
    }

    /**
     * @return ?array
     */
    public function getProjectType(): ?array
    {
        return $this->projectType;
    }

    /**
     * @param int $productId
     */
    public function setProductId(int $productId): void
    {
        $this->productId = $productId;
    }

    /**
     * @param array $postalCode
     */
    public function setPostalCode(array $postalCode): void
    {
        $this->postalCode = $postalCode;
    }

    /**
     * @param array $broadcastMedium
     */
    public function setBroadcastMedium(array $broadcastMedium): void
    {
        $this->broadcastMedium = $broadcastMedium;
    }

    /**
     * @param array $projectType
     */
    public function setProjectType(array $projectType): void
    {
        $this->projectType = $projectType;
    }

    /**
     * @return string
     */
    public function getStartDate(): string
    {
        return $this->startDate;
    }

    /**
     * @return string
     */
    public function getEndDate(): string
    {
        return $this->endDate;
    }
}