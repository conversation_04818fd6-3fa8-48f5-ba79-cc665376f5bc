.cardOfferExpert360 {
  padding: 1em;
  min-height: 17.875em;
  box-shadow: 0 0.1em 0.5em 0 rgba(0, 0, 0, 0.16);
  border-radius: 0.3em;
  display: flex;
  margin: auto;
  margin-top: 1em;

  .cardImage {
    height: 15.875em;
    width: 15.875em;
    background-size: cover;
    flex-shrink: 0;
  }
  .cardContent {
    flex-grow: 1;
    margin-left: 1.5em;

    .bannerCard {
      display: flex;
      justify-content: space-between;
      min-height: 2.625em;
      .wrapperTags {
        > div {
          margin-bottom: 0.4em;
        }
      }
      .wrapperRelativeInfos {
        min-width: 9em;
        text-align: right;
        display: flex;
        flex-direction: column;
        p {
          font-weight: 700;
          color: $saphir;
        }
        span {
          font-size: 0.75em;
          color: $black;
        }
      }
    }
    .titleCard {
      margin-top: 0.625em;
      margin-bottom: 0.5em;

      p {
        font-size: 1.5em;
        font-weight: 700;
        color: $black;
      }
    }
    .descriptionCard {
      width: 70%;
      color: $black;
      overflow: auto;

      @include verySmallDesktop() {
        width: 100%;
      }

      .contentDescriptionCard{
        font-size: 0.75em;
        ul {
          padding-left: 1.8em;
          margin-top: 0.2em;
          li {
            list-style-type: disc;
          }
        }
      }

      .commentsWrapper {
        margin-top: 1.563em;
        max-width: 34.125em;

        p{
          font-weight: 600;
          font-size: 0.875em;
          margin-bottom: 0.1em;
        }
        
        textarea {
          resize: none;
          border: 1px solid #929292;
          box-sizing: border-box;
          border-radius: 0.25em;
          margin-top: 0.313em;
          width: 100%;
          padding: 0.5em 0.75em;
          height: 5em;

          &:focus{
            border-color: $back;
            border: 1px solid $back;
            outline: none !important;
          }
        }
        span{
          font-size: 0.75em;
          font-weight: 400;
        }
      }
    }
  }

  &.low {
    .cardContent {
      .bannerCard {
        .wrapperRelativeInfos {
          p {
            color: $saphir;
          }
        }
      }
      .titleCard {
        p {
          color: $darkerSaphir;
        }
      }
    }
  }
  &.medium {
    .cardContent {
      .bannerCard {
        .wrapperRelativeInfos {
          p {
            color: $normalGrass;
          }
        }
        .wrapperTags {
          > div {
            background-color: $lightGrass;
          }
        }
      }
      .titleCard {
        p {
          color: $darkGreen;
        }
      }
    }
  }
  &.high {
    .cardContent {
      .bannerCard {
        .wrapperRelativeInfos {
          p {
            color: $candy;
          }
        }
        .wrapperTags {
          > div {
            background-color: $lighterCandy;
          }
        }
      }
      .titleCard {
        p {
          color: $darkerCandy;
        }
      }
    }
  }
}
#cardBrandContent {
  min-height: 20.938em;
  .descriptionCard {
    min-height: 20.938em;
  }
}
