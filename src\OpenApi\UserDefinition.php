<?php

declare(strict_types=1);

namespace App\OpenApi;

use ApiPlatform\Core\OpenApi\Model\Operation;
use ApiPlatform\Core\OpenApi\Model\Response;

class UserDefinition
{
    public static function getGetSelfOperation(): Operation
    {
        $responses = [
            '200' => new Response('User resource'),
            '404' => new Response('Resource not found'),
        ];

        return new Operation('getSelfUserItem', ['User'], $responses, 'Retrieves Self User resource.', 'Retrieves Self User resource.');
    }
}
