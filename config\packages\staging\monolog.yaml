monolog:
    channels: [deprecation]
    handlers:
        main:
            type: rotating_file
            max_files: 30
            path: "%kernel.logs_dir%/%kernel.environment%.log"
            level: debug
            channels: ["!event", "!deprecation"]
        # uncomment to get logging in your browser
        # you may have to allow bigger header sizes in your Web server configuration
        #firephp:
        #    type: firephp
        #    level: info
        #chromephp:
        #    type: chromephp
        #    level: info
        console:
            type: console
            process_psr_3_messages: false
            channels: ["!event", "!doctrine", "!console"]
        deprecation:
            type: stream
            channels: [deprecation]
            path: "%kernel.logs_dir%/%kernel.environment%.deprecations.log"
