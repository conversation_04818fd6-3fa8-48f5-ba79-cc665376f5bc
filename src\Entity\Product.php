<?php

declare(strict_types=1);

namespace App\Entity;

use App\Repository\ProductRepository;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass=ProductRepository::class)
 * @ORM\Table(name="product_adfactory")
 */
class Product
{
    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     */
    private int $id;

    /**
     * @ORM\Column(name="name", type="string", length=50, nullable=true)
     */
    private ?string $name;

    /**
     * @ORM\Column(name="admanager_adunit", type="string", length=50, nullable=true)
     */
    private ?string $admanagerAdunit;

    /**
     * @ORM\Column(name="admanager_device", type="string", length=50, nullable=true)
     */
    private ?string $admanagerDevice;

    /**
     * @ORM\Column(name="admanager_format_sizes", type="string", length=50, nullable=true)
     */
    private ?string $admanagerFormatSizes;

    /**
     * @ORM\Column(name="smart_pages", type="string", length=50, nullable=true)
     */
    private ?string $smartPages;

    /**
     * @ORM\Column(name="smart_formats", type="string", length=50, nullable=true)
     */
    private ?string $smartFormats;

    /**
     * @ORM\Column(name="id_product_sf", type="string", length=50, nullable=true)
     */
    private ?string $productIdSF;

    /**
     * @ORM\Column(name="admanager_custom_target_pos", type="json_array", nullable=true)
     */
    private ?array $admanagerCustomTargetPos;


    /**
     * Get the value of id
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * Get the value of name
     * @return string
     */
    public function getName(): ?string
    {
        return $this->name;
    }

    /**
     * Get the value of admanagerDevice
     * @return string
     */
    public function getAdmanagerDevice(): ?string
    {
        return $this->admanagerDevice;
    }

    /**
     * Get the value of admanagerFormatSizes
     * @return string
     */
    public function getAdmanagerFormatSizes(): ?string
    {
        return $this->admanagerFormatSizes;
    }

    /**
     * Get the value of admanagerAdunit
     * @return string
     */
    public function getAdmanagerAdunit(): ?string
    {
        return $this->admanagerAdunit;
    }

    /**
     * Get the value of smartPages
     * @return string
     */
    public function getSmartPages(): ?string
    {
        return $this->smartPages;
    }

    /**
     * Get the value of smartFormats
     * @return string
     */
    public function getSmartFormats(): ?string
    {
        return $this->smartFormats;
    }

    /**
     * Get the value of productIdSF
     * @return string
     */
    public function getProductIdSF(): ?string
    {
        return $this->productIdSF;
    }

    /**
     * Get the value of getAdmanagerCustomTargetPos
     * @return array
     */
    public function getAdmanagerCustomTargetPos(): ?array
    {
        return $this->admanagerCustomTargetPos;
    }
}
