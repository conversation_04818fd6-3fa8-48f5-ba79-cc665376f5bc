<?php

declare(strict_types=1);

namespace App\Hexagonal\Client\Domain;

use App\Entity\Client;

class ClientService
{
    private ClientRepositoryPort $clientRepository;

    /**
     * ClientService constructor.
     * @param ClientRepositoryPort $clientRepository
     */
    public function __construct(ClientRepositoryPort $clientRepository)
    {
        $this->clientRepository = $clientRepository;
    }

    /**
     * Get by clientId & filter intensity by $forecastResults
     * @param string $clientId
     * @param array $forecastResults
     * @return Client
     */
    public function getByClientIdAndForecast(string $clientId, array $forecastResults): Client
    {
        $client = $this->clientRepository->getByClientId($clientId);

        $totalImpressions = 0;
        foreach ($forecastResults as $forecastResult) {
            $totalImpressions += $forecastResult->getAvailable();
        }

        // filter intensity
        $intensities = $client->getIntensity();
        $filteredIntensities = [];
        foreach ($intensities as $intensity) {
            if ($intensity['impressions'] <= 1.3 * $totalImpressions) {
                $available = ['available' => true];
            } else {
                $available = ['available' => false];
            }

            $filteredIntensities[] = $available + $intensity;
        }

        // remove unavailable intensity
        $client->setIntensity($filteredIntensities);

        return $client;
    }
}