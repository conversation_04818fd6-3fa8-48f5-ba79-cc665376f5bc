<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210413125441 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE SEQUENCE admanager_custom_target_pos_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE TABLE admanager_custom_target_pos (id INT NOT NULL, label VARCHAR(255) DEFAULT NULL, id_admanager bigint NOT NULL, id_custom_pos INT NOT NULL, PRIMARY KEY(id))');
        $this->addSql('ALTER TABLE product_adfactory ADD admanager_custom_target_pos json DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN product_adfactory.admanager_custom_target_pos IS \'(DC2Type:json_array)\'');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('DROP SEQUENCE admanager_custom_target_pos_id_seq CASCADE');
        $this->addSql('DROP TABLE admanager_custom_target_pos');
        $this->addSql('ALTER TABLE product_adfactory DROP admanager_custom_target_pos');
    }
}
