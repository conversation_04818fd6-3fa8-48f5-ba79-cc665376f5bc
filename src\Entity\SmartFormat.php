<?php

namespace App\Entity;

use App\Repository\SmartFormatRepository;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass=SmartFormatRepository::class)
 * @ORM\Table(name="smart_format")
 */
class SmartFormat
{
    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\Column(name="label", type="string", length=50, nullable=true)
     */
    private $label;

    /**
     * @ORM\Column(name="smart_id", type="string", length=50, nullable=true)
     */
    private $smartId;


    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @return string
     */
    public function getLabel(): string
    {
        return $this->label;
    }

    /**
     * @return int
     */
    public function getSmartId(): int
    {
        return $this->smartId;
    }
}
