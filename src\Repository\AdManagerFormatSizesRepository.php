<?php

namespace App\Repository;

use App\Entity\AdManagerFormatSizes;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method AdManagerFormatSizes|null find($id, $lockMode = null, $lockVersion = null)
 * @method AdManagerFormatSizes|null findOneBy(array $criteria, array $orderBy = null)
 * @method AdManagerFormatSizes[]    findAll()
 * @method AdManagerFormatSizes[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class AdManagerFormatSizesRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, AdManagerFormatSizes::class);
    }

    /**
     */
    public function findAllByProjectID(int $projectId): array
    {
        $qb = $this->createQueryBuilder('a')
            ->andWhere('a.projectId = :projectId')
            ->setParameter('projectId', $projectId);

        return $qb->getQuery()->getResult();
    }
}
