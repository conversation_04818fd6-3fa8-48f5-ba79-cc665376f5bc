<?php

declare(strict_types=1);

namespace App\Hexagonal\Product\Adapters\Database;

use Doctrine\ORM\EntityManagerInterface;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepositoryInterface;
use App\Hexagonal\Product\Domain\Database\ProductRepositoryPort;
use App\Entity\Product;

final class ProductRepositoryPSQLAdapter implements ProductRepositoryPort
{
    private EntityManagerInterface $entityManager;
    private ServiceEntityRepositoryInterface $repository;

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
        $this->repository = $this->entityManager->getRepository(Product::class);
    }

    public function get(int $id): ?Product
    {
        return $this->repository->findOneBy(['id' => $id]);
    }
}
