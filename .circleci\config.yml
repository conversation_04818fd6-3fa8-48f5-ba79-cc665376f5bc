version: 2.1

aliases:
  - &awscli
    docker:
      - image: $AWS_ECR_CENTRAL_SHARED_SERVICES/seloger/docker-awscli:latest
        aws_auth:
          aws_access_key_id: $AWS_ACCESS_KEY_ID_CENTRAL_SHARED_SERVICES
          aws_secret_access_key: $AWS_SECRET_ACCESS_KEY_CENTRAL_SHARED_SERVICES
  - &deploy
    docker:
      - image: $AWS_ECR_CENTRAL_SHARED_SERVICES/seloger/docker-kubecli:latest
        aws_auth:
          aws_access_key_id: $AWS_ACCESS_KEY_ID_CENTRAL_SHARED_SERVICES
          aws_secret_access_key: $AWS_SECRET_ACCESS_KEY_CENTRAL_SHARED_SERVICES
  - &deploy_steps
    steps:
      - attach_workspace:
          at: ~/project
      - run:
          name: Set ENV vars
          command: |
            sed -i "s/{circle_repo}/$(echo $CIRCLE_PROJECT_REPONAME | sed 's/_/-/g' )/g; s/{circle_branch}/$CIRCLE_BRANCH/g" ./.circleci/vars.yml;
            if [[ $CIRCLE_BRANCH == 'int1' || $CIRCLE_BRANCH == 'int2' || $CIRCLE_BRANCH == 'int3' ]]; then ( sed -i "s/{rds_schema}/postgres-int/g" ./.circleci/vars.yml ); else ( sed -i "s/{rds_schema}/postgres/g" ./.circleci/vars.yml ); fi;
      - run:
          name: Deploy resources on cluster
          command: |
            ./.k8s/scripts/app.py --context $CONTEXT
  - &deploy_ecr_steps
    steps:
      - attach_workspace:
          at: /tmp
      - setup_remote_docker
      - checkout
      - run:
          name: Deploy container
          command: |
            aws-cloud ecr deploy --organization groupeseloger \
              --account $AWS_ACCOUNT_GROUP-$AWS_ENV \
              --app-name $CIRCLE_PROJECT_REPONAME \
              --allow-account ************ \
              --allow-account ************

jobs:

  build:
    <<: *awscli
    steps:
      - attach_workspace:
          at: ~/project
      - setup_remote_docker
      - checkout
      - run:
          name: Build container
          command: |
            aws-cloud ecr build \
              --organization groupeseloger \
              --account $AWS_ACCOUNT_GROUP-$AWS_ENV \
              --app-name $CIRCLE_PROJECT_REPONAME
      - persist_to_workspace:
          root: /tmp
          paths:
            - images
      - persist_to_workspace:
          root: ~/project
          paths:
            - .circleci

  deploy_ecr_staging:
    <<: *awscli
    <<: *deploy_ecr_steps

  deploy_ecr_production:
    <<: *awscli
    <<: *deploy_ecr_steps

  deploy-staging:
    <<: *deploy
    environment:
      CONTEXT: staging
      AWS_REGION: eu-west-1
    <<: *deploy_steps

  deploy-production:
    <<: *deploy
    environment:
      CONTEXT: production
      AWS_REGION: eu-west-1
    <<: *deploy_steps

workflows:
  staging:
    jobs:
      - build:
          context: sl_central-shared-services
          filters:
            branches:
              only:
                - stage
                - /int[1-3]/
      - deploy_ecr_staging:
          context: sl_digital-nonprod
          requires:
            - build
      - hold-deploy:
          type: approval
          requires:
            - deploy_ecr_staging
      - deploy-staging:
          context: sl_digital-nonprod
          requires:
            - hold-deploy
  production:
    jobs:
      - build:
          context: sl_central-shared-services
          filters:
            branches:
              only:
                - master
      - deploy_ecr_staging:
          context: sl_digital-nonprod
          requires:
            - build
      - deploy-staging:
          context: sl_digital-nonprod
          requires:
            - deploy_ecr_staging
      - promote:
          type: approval
          requires:
            - deploy-staging
      - deploy_ecr_production:
          context: sl_digital-prod
          requires:
            - promote
      - deploy-production:
          context: sl_digital-prod
          requires:
            - deploy_ecr_production
