<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\Client;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

class ClientRepository extends ServiceEntityRepository
{
    private $em;

    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Client::class);
        $this->em = $this->getEntityManager();
        // disable logging to avoid out of memory
        $this->em->getConnection()->getConfiguration()->setSQLLogger(null);
    }

    /**
     * @param string $clientId
     * @return array
     */
    public function getAllByClientId(string $clientId): array
    {
        $qb = $this->createQueryBuilder('d');
        $clientId = strtoupper($clientId);

        return $qb
            ->where($qb->expr()->like('d.clientId', ':clientId'))
            ->setParameter('clientId', $clientId . '%')
            ->orderBy('d.clientId')
            ->setMaxResults(20)
            ->getQuery()
            ->getResult();
    }

    /**
     * Bulk upsert clients
     * @param array $clients
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     * @throws \Doctrine\Persistence\Mapping\MappingException
     */
    public function bulkSave(array $clients): void
    {
        $existingClients = $this->findBy(['clientId' => array_keys($clients)]);

        // update
        $existingClientIds = [];
        foreach ($existingClients as $existingClient) {
            $clientId = $existingClient->getClientId();
            $existingClientIds[$clientId] = $clientId;
            $existingClient->setIntensity($clients[$clientId]->getIntensity());
        }

        // insert
        $newClients = array_diff_key($clients, $existingClientIds);
        foreach ($newClients as $newClient) {
            $this->em->persist($newClient);
        }

        $this->em->flush();
        $this->em->clear();
    }
}
