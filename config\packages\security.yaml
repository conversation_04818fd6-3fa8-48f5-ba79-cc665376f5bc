security:
    enable_authenticator_manager: true

    providers:
        users:
            entity:
                class: 'App\Entity\User'
                property: 'email'

    firewalls:
        dev:
            pattern: ^/(_(profiler|wdt)|css|images|js)/
            security: false
        main:
            custom_authenticators:
                - App\Hexagonal\Security\Adapters\JWT\TokenAuthenticator\JWTTokenAuthenticator
            stateless: true

    # Note: Only the *first* access control that matches will be used
    access_control:
        - { path: ^/bff/api/healthcheck/liveness, roles: PUBLIC_ACCESS }
        - { path: ^/bff/api/healthcheck/readiness, roles: PUBLIC_ACCESS }
        - { path: ^/bff/api/counts, roles: IS_AUTHENTICATED_FULLY }
        - { path: ^/bff/api/clients, roles: IS_AUTHENTICATED_FULLY }
        - { path: ^/bff/api/forecasts, roles: IS_AUTHENTICATED_FULLY }
        - { path: ^/bff/api/places, roles: IS_AUTHENTICATED_FULLY }
        - { path: ^/bff/api/users, roles: IS_AUTHENTICATED_FULLY }
        - { path: ^/bff/api/pdf, roles: IS_AUTHENTICATED_FULLY }
        - { path: ^/bff/api, roles: PUBLIC_ACCESS }
        - { path: ^/, roles: ROLE_ADMIN }
