<?php

declare(strict_types=1);

namespace App\Hexagonal\User\Adapters\Database;

use Symfony\Component\Security\Core\User\UserInterface;
use App\Hexagonal\User\Domain\Database\UserRepositoryPort;
use App\Entity\User;

final class UserRepositoryInMemoryAdapter implements UserRepositoryPort
{
    private array $users = [];
    private int $index;

    public function __construct()
    {
        $this->index = 1;
        $this->initRepositoryWithUsers();
    }

    private function initRepositoryWithUsers(): void
    {
        $user1 = new User(null, '<EMAIL>', 'firstNameUser1', 'nameUser1');
        $user2 = new User(null, '<EMAIL>', 'firstNameUser2', 'nameUser2');
        $user3 = new User(null, '<EMAIL>', 'Gon3', 'Freecs3');

        $user1 = $this->create($user1);
        $user2 = $this->create($user2);
        $user3 = $this->create($user3);

        $this->update($user1);
        $this->update($user2);
        $this->update($user3);
    }

    public function create(UserInterface $user): UserInterface
    {
        $userIndex = $this->index++;
        $user = new User($userIndex, $user->getEmail(), $user->getFirstName(), $user->getLastName());
        $this->users[$userIndex] = $user;

        return $user;
    }

    public function getByEmail(string $email): ?UserInterface
    {
        $users = array_values(
            array_filter(
                $this->users,
                function ($user) use ($email) {
                    return $user->getEmail() === $email;
                },
                ARRAY_FILTER_USE_BOTH
            )
        );

        return isset($users[0]) ? $users[0] : null;
    }

    public function get(int $id): ?UserInterface
    {
        return isset($this->users[$id]) ? $this->users[$id] : null;
    }

    public function getAll(): array
    {
        return $this->users;
    }

    public function update(UserInterface $user): ?UserInterface
    {
        if (!$this->get($user->getId())) {
            return null;
        }

        // persist
        $this->users[$user->getId()] = $user;

        return $user;
    }
}
