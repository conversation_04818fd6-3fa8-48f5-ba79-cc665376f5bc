<?php

declare(strict_types=1);

namespace App\Hexagonal\Forecast\Adapters\Database;

use App\Entity\AdManagerFormatSizes;
use App\Hexagonal\Forecast\Domain\Database\AdManagerFormatRepositoryPort;
use Doctrine\Persistence\ManagerRegistry;

final class AdManagerFormatRepositoryPSQLAdapter implements AdManagerFormatRepositoryPort
{
    private ManagerRegistry $managerRegistry;

    public function __construct(ManagerRegistry $managerRegistry)
    {
        $this->managerRegistry = $managerRegistry;
    }

    /**
     * @inheritDoc
     */
    public function getByIds(array $ids): array
    {
        $adUnitRepository = $this->managerRegistry->getRepository(AdManagerFormatSizes::class);
        return $adUnitRepository->findBy(['id' => $ids]);
    }
}