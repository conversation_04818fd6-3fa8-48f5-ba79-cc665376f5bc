<?php

namespace App\Entity;

use App\Repository\AdManagerFormatSizesRepository;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass=AdManagerFormatSizesRepository::class)
 * @ORM\Table(name="admanager_format_sizes")
 */
class AdManagerFormatSizes
{
    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\Column(name="native_format_name", type="string", length=255, nullable=true)
     */
    private $nativeFormatName;

    /**
     * @ORM\Column(name="size_width", type="string", length=255)
     */
    private $sizeWidth;

    /**
     * @ORM\Column(name="size_height", type="string", length=255)
     */
    private $sizeHeight;

    /**
     * @ORM\Column(name="native_id", type="integer")
     */
    private $nativeId;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getNativeFormatName(): ?string
    {
        return $this->nativeFormatName;
    }

    public function setNativeFormatName(?string $nativeFormatName): self
    {
        $this->nativeFormatName = $nativeFormatName;

        return $this;
    }

    public function getSizeWidth(): ?string
    {
        return $this->sizeWidth;
    }

    public function setSizeWidth(string $sizeWidth): self
    {
        $this->sizeWidth = $sizeWidth;

        return $this;
    }

    public function getSizeHeight(): ?string
    {
        return $this->sizeHeight;
    }

    public function setSizeHeight(string $sizeHeight): self
    {
        $this->sizeHeight = $sizeHeight;

        return $this;
    }

    public function getNativeId(): ?int
    {
        return $this->nativeId;
    }

    public function setNativeId(int $nativeId): self
    {
        $this->nativeId = $nativeId;

        return $this;
    }
}
