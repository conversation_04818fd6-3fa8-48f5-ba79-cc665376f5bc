<?php

declare(strict_types=1);

namespace App\Hexagonal\Forecast\Adapters\Database\GoogleAdManager;

use DateTime;
use DateTimeZone;
use Google\AdsApi\AdManager\Util\v202102\AdManagerDateTimes;
use Google\AdsApi\AdManager\v202102\AdUnitTargeting;
use Google\AdsApi\AdManager\v202102\CostType;
use Google\AdsApi\AdManager\v202102\CreativePlaceholder;
use Google\AdsApi\AdManager\v202102\CreativeSizeType;
use Google\AdsApi\AdManager\v202102\CustomCriteria;
use Google\AdsApi\AdManager\v202102\CustomCriteriaComparisonOperator;
use Google\AdsApi\AdManager\v202102\CustomCriteriaSet;
use Google\AdsApi\AdManager\v202102\CustomCriteriaSetLogicalOperator;
use Google\AdsApi\AdManager\v202102\DeliveryRateType;
use Google\AdsApi\AdManager\v202102\DeviceCategoryTargeting;
use Google\AdsApi\AdManager\v202102\FrequencyCap;
use Google\AdsApi\AdManager\v202102\Goal;
use Google\AdsApi\AdManager\v202102\InventoryTargeting;
use Google\AdsApi\AdManager\v202102\LineItem;
use Google\AdsApi\AdManager\v202102\LineItemType;
use Google\AdsApi\AdManager\v202102\Size;
use Google\AdsApi\AdManager\v202102\Targeting;
use Google\AdsApi\AdManager\v202102\Technology;
use Google\AdsApi\AdManager\v202102\TechnologyTargeting;
use Google\AdsApi\AdManager\v202102\UnitType;

class GoogleLineItem
{
    private LineItem $lineItem;
    private const KEY_ID_CUSTOM_TARGET_DEPARTMENT = 310968;
    private const KEY_ID_CUSTOM_TARGET_CP = 311088;

    public function __construct(LineItem $lineItem)
    {
        $this->lineItem = $lineItem;
    }

    public function setLineItem(GoogleOptions $options): void
    {
        $this->lineItem->setCostType(CostType::CPM);

        // Line Item Type
        $this->lineItem->setLineItemType(LineItemType::STANDARD);
        $this->lineItem->setPriority($options->getLinePriority());

        // Delivery Settings
        $this->lineItem->setStartDateTime(
            AdManagerDateTimes::fromDateTime(
                new DateTime($options->getStartDate(), new DateTimeZone('CET'))
            )
        );
        $this->lineItem->setEndDateTime(
            AdManagerDateTimes::fromDateTime(
                new DateTime($options->getEndDate(), new DateTimeZone('CET'))
            )
        );

        // Set Goal
        $goal = new Goal();
        $goal->setUnitType(UnitType::IMPRESSIONS);
        $this->lineItem->setPrimaryGoal($goal);

        // Adjust Delivery
        $frequencyCap = $options->getFrequencyCap();
        $frequencyCap = $this->getFrequencyCap($frequencyCap[0], $frequencyCap[1], $frequencyCap[2]);
        $this->lineItem->setFrequencyCaps([$frequencyCap]);

        $this->lineItem->setDeliveryRateType(DeliveryRateType::EVENLY);

        // Set RoadBlockingType (ONLY_ONE or ONE_OR_MORE = default value)
        $this->lineItem->setRoadblockingType($options->getRoadBlockingType());

        // Expected Creatives
        $creativePlaceHolders = $this->getCreativePlaceHolders($options->getFormatSizes());
        $this->lineItem->setCreativePlaceholders($creativePlaceHolders);

        //Targeting
        $targeting = $this->getTargeting($options);
        $this->lineItem->setTargeting($targeting);
    }

    /**
     * @return LineItem
     */
    public function getLineItem(): LineItem
    {
        return $this->lineItem;
    }

    /**
     * extractArrayCPForCreateCustomTargeting
     *
     * @param  array $customTargetings
     * @return array
     */
    private function extractArrayCPForCreateCustomTargeting(array $customTargetings): array
    {
        $aCusTargetCP = [];
        foreach ($customTargetings as $aCusTargeting) {
            if ($aCusTargeting['id'] === self::KEY_ID_CUSTOM_TARGET_CP || $aCusTargeting['id'] === self::KEY_ID_CUSTOM_TARGET_DEPARTMENT) {
                $aCusTargetCP[] =  $aCusTargeting;
            }
        }

        return $aCusTargetCP;
    }

    /**
     * createCustomTargeting
     *
     * @param  array $customTargetings
     * @return CustomCriteriaSet
     */
    private function createCustomTargeting(array $customTargetings): CustomCriteriaSet
    {
        $customCriteriaSetFinal = new CustomCriteriaSet();
        $customCriteriaSetFinal->setLogicalOperator(CustomCriteriaSetLogicalOperator::AND_VALUE);

        if (count($customTargetings) > 2) {
            $aCusTargetCP = $this->extractArrayCPForCreateCustomTargeting($customTargetings);

            $customCriteriaCP = new CustomCriteriaSet();
            $customCriteriaCP->setLogicalOperator(CustomCriteriaSetLogicalOperator::AND_VALUE);
            $customCriteriaArr = $this->getCustomTargetings($aCusTargetCP);
            $customCriteriaCP->setChildren($customCriteriaArr);

            $customCriteriaPosition = new CustomCriteriaSet();
            array_pop($customTargetings); // delete array target CP => array only positions
            $customCriteriaPosition->setLogicalOperator(CustomCriteriaSetLogicalOperator::OR_VALUE);
            $customCriteriaArrPosition = $this->getCustomTargetings($customTargetings);
            $customCriteriaPosition->setChildren($customCriteriaArrPosition);

            $customCriteriaSetFinal->setChildren([$customCriteriaPosition, $customCriteriaCP]);
        } else {
            $customCriteriaArr = $this->getCustomTargetings($customTargetings);
            $customCriteriaSetFinal->setChildren($customCriteriaArr);
        }

        return $customCriteriaSetFinal;
    }

    /**
     * @param GoogleOptions $options
     * @return Targeting
     */
    private function getTargeting(GoogleOptions $options): Targeting
    {
        // AdUnits Targeting
        $inventoryTargeting = new InventoryTargeting();
        $adUnitsTarget = $this->getAdUnits($options->getAdUnitIds());
        $inventoryTargeting->setTargetedAdUnits($adUnitsTarget);

        // Custom Targeting : Pos
        $customCriteriaChildren = [];
        foreach ($options->getCustomTargetings() as $customTargetings) {
            $customCriteriaChildren[] = $this->createCustomTargeting($customTargetings);
        }

        // Root Set
        $customCriteriaRootSet = new CustomCriteriaSet();
        $customCriteriaRootSet->setLogicalOperator(CustomCriteriaSetLogicalOperator::OR_VALUE);
        $customCriteriaRootSet->setChildren($customCriteriaChildren);

        // Device Category Targeting
        $deviceCategoryTargeting = new DeviceCategoryTargeting();
        $devices = $this->getDevices($options->getDeviceIds());
        $deviceCategoryTargeting->setTargetedDeviceCategories($devices);
        $technologyTargeting = new TechnologyTargeting();
        $technologyTargeting->setDeviceCategoryTargeting($deviceCategoryTargeting);

        $targeting = new Targeting();
        $targeting->setCustomTargeting($customCriteriaRootSet);
        $targeting->setInventoryTargeting($inventoryTargeting);
        $targeting->setTechnologyTargeting($technologyTargeting);

        return $targeting;
    }

    /**
     * Targeting > Inventory : Ad Units
     *
     * @param array $adUnits
     * @return array $adUnitsTargetArr
     */
    private function getAdUnits(array $adUnits): array
    {
        $adUnitsTargetArr = [];
        foreach ($adUnits as $adUnit) {
            $adUnitTargeting = new AdUnitTargeting();
            $adUnitTargeting->setAdUnitId($adUnit->getAdmanagerId());
            $adUnitTargeting->setIncludeDescendants(true);
            $adUnitsTargetArr[] = $adUnitTargeting;
        }

        return $adUnitsTargetArr;
    }

    /**
     * Targeting : Custom Targeting
     *
     * @param array $customTargetings
     * @return array $customCriteriaArr
     */
    private function getCustomTargetings(array $customTargetings): array
    {
        $customCriteriaArr = [];
        foreach ($customTargetings as $customTargeting) {
            $customCriteria = new CustomCriteria();
            $customCriteria->setKeyId($customTargeting['id']);
            $customCriteria->setOperator(CustomCriteriaComparisonOperator::IS);
            $customCriteria->setValueIds([$customTargeting['target']]);
            $customCriteriaArr[] = $customCriteria;
        }

        return $customCriteriaArr;
    }

    /**
     * Targeting : Device Category
     *
     * @param array $devices
     * @return array $devicesArr
     */
    private function getDevices(array $devices): array
    {
        $devicesArr = [];
        foreach ($devices as $device) {
            $technology = new Technology();
            $technology->setId($device->getAdmanagerId());
            $devicesArr[] = $technology;
        }

        return $devicesArr;
    }

    /**
     * Adjust Delivery - Frequency
     *
     * @param int $maxImpressions
     * @param int $numTimeUnits
     * @param string $timeUnit
     * @return FrequencyCap $frequencyCap
     */
    private function getFrequencyCap(int $maxImpressions, int $numTimeUnits, string $timeUnit): FrequencyCap
    {
        $frequencyCap = new FrequencyCap();
        $frequencyCap->setMaxImpressions($maxImpressions);
        $frequencyCap->setNumTimeUnits($numTimeUnits);
        $frequencyCap->setTimeUnit($timeUnit);

        return $frequencyCap;
    }

    /**
     * Expected Creatives
     *
     * @param array $formats
     * @return array $creativePlaceHolders
     */
    private function getCreativePlaceHolders(array $formats): array
    {
        $creativePlaceHolders = [];
        foreach ($formats as $format) {
            $size = new Size();
            $size->setWidth($format->getSizeWidth());
            $size->setHeight($format->getSizeHeight());
            $size->setIsAspectRatio(false);
            $creativePlaceHolder = new CreativePlaceholder();
            if ($format->getNativeId() != 0) {
                $creativePlaceHolder->setCreativeTemplateId($format->getNativeId());
                $creativePlaceHolder->setCreativeSizeType(CreativeSizeType::NATIVE);
            }
            $creativePlaceHolder->setSize($size);
            $creativePlaceHolders[] = $creativePlaceHolder;
        }

        return $creativePlaceHolders;
    }
}
