[AD_MANAGER]
; Required Ad Manager API properties. Details can be found at:
; https://developers.google.com/ad-manager/docs/soap_xml
networkCode = "*********"
applicationName = "myadfactory"

; Optional additional Ad Manager API settings.
; endpoint = "https://ads.google.com/"

[OAUTH2]
; Required OAuth2 credentials. Uncomment and fill in the values for the
; appropriate flow based on your use case. See the README for guidance:
; https://github.com/googleads/googleads-php-lib/blob/master/README.md#getting-started

; For service account flow.
jsonKeyFilePath = "/var/www/config/admanager_api_key.json"
scopes = "https://www.googleapis.com/auth/dfp"
; impersonatedEmail = "INSERT_EMAIL_OF_ACCOUNT_TO_IMPERSONATE_HERE"

; For installed application or web application flow.
; clientId = "************-mgc7ijkijiedoqflgnl0bfq61undd0ok.apps.googleusercontent.com"
; clientSecret = "uhOJjGKexfRs-4TXEBM7THc9"
; refreshToken = "INSERT_OAUTH2_REFRESH_TOKEN_HERE"

[SOAP]
; Optional SOAP settings. See SoapSettingsBuilder.php for more information.
; compressionLevel = <COMPRESSION_LEVEL>

[CONNECTION]
; Optional proxy settings to be used by requests.
; If you don't have username and password, just specify host and port.
; proxy = "protocol://user:pass@host:port"

[LOGGING]
; Optional logging settings.
; soapLogFilePath = "path/to/your/soap.log"
; soapLogLevel = "INFO";
; reportDownloaderLogFilePath = "path/to/your/report-downloader.log"
; reportDownloaderLogLevel = "INFO"
