<?php

declare(strict_types=1);

namespace App\OpenApi;

use ApiPlatform\Core\OpenApi\Model\Parameter;

class ForecastDefinition
{
    public static function getGetParameters(): array
    {
        $parameters = [
            new Parameter('postalCode', 'query', 'postal code(s) or dpt(s)', true, false, false, ['type' => 'string']),
            new Parameter('productId', 'query', 'product id', true, false, false, ['type' => 'integer']),
            new Parameter('broadcastMedium', 'query', 'broadcast medium(s)', true, false, false, ['type' => 'string']),
            new Parameter('projectType', 'query', 'project type(s)', true, false, false, ['type' => 'string']),
        ];

        return $parameters;
    }
}
