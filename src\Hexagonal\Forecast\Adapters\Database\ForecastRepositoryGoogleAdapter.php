<?php

declare(strict_types=1);

namespace App\Hexagonal\Forecast\Adapters\Database;

use App\Hexagonal\Forecast\Domain\Database\ForecastRepositoryPort;
use App\Hexagonal\Forecast\Domain\Model\ForecastParams;
use App\Hexagonal\Forecast\Adapters\Database\GoogleAdManager\GoogleForecastService;
use App\Hexagonal\Forecast\Adapters\Database\GoogleAdManager\GoogleLineItem;
use App\Hexagonal\Forecast\Adapters\Database\GoogleAdManager\GoogleOptions;

final class ForecastRepositoryGoogleAdapter implements ForecastRepositoryPort
{
    private GoogleForecastService $googleForecastService;
    private GoogleLineItem $googleLineItem;
    private GoogleOptions $googleOptions;

    public function __construct(GoogleForecastService $googleForecastService,
                                GoogleLineItem $googleLineItem,
                                GoogleOptions $googleOptions)
    {
        $this->googleForecastService = $googleForecastService;
        $this->googleLineItem = $googleLineItem;
        $this->googleOptions = $googleOptions;
    }

    public function get(ForecastParams $forecastParams): array
    {
        // translate the request params in google options
        $this->googleOptions->setOptions($forecastParams);

        // use the options to set the lineItem
        $this->googleLineItem->setLineItem($this->googleOptions);

        return $this->googleForecastService->getForecast($this->googleLineItem->getLineItem());
    }
}