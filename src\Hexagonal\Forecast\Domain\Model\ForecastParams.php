<?php

declare(strict_types=1);

namespace App\Hexagonal\Forecast\Domain\Model;

use App\Entity\Product;

class ForecastParams
{
    private Product $product;
    private array $postalCode;
    private array $broadcastMedium;
    private array $projectType;
    private string $startDate;
    private string $endDate;

    public function setParams(Product $product,
                              array $postalCode,
                              array $broadcastMedium,
                              array $projectType,
                              string $startDate,
                              string $endDate): void
    {
        $this->product = $product;
        $this->postalCode = $postalCode;
        $this->broadcastMedium = $broadcastMedium;
        $this->projectType = $projectType;
        $this->startDate = $startDate;
        $this->endDate = $endDate;
    }

    /**
     * @return Product
     */
    public function getProduct(): Product
    {
        return $this->product;
    }

    /**
     * @return array
     */
    public function getPostalCode(): array
    {
        return $this->postalCode;
    }

    /**
     * @return array
     */
    public function getBroadcastMedium(): array
    {
        return $this->broadcastMedium;
    }

    /**
     * @return array
     */
    public function getProjectType(): array
    {
        return $this->projectType;
    }

    /**
     * @return string
     */
    public function getStartDate(): string
    {
        return $this->startDate;
    }

    /**
     * @return string
     */
    public function getEndDate(): string
    {
        return $this->endDate;
    }

    /**
     * @param array $postalCode
     */
    public function setPostalCode(array $postalCode): void
    {
        $this->postalCode = $postalCode;
    }
}