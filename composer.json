{"type": "project", "license": "proprietary", "require": {"php": "^7.4", "ext-ctype": "*", "ext-iconv": "*", "api-platform/api-pack": "^1.2", "codercat/jwk-to-pem": "^1.0", "composer/package-versions-deprecated": "1.10.99.1", "doctrine/annotations": "^1.11", "doctrine/doctrine-bundle": "^2.1", "doctrine/doctrine-migrations-bundle": "^3.0", "doctrine/orm": "^2.7", "firebase/php-jwt": "^5.2", "googleads/googleads-php-lib": "^52.0", "nesk/puphpeteer": "^2.0", "ocramius/package-versions": "^1.9", "sensio/framework-extra-bundle": "^5.5", "symfony/asset": "5.2.*", "symfony/console": "5.2.*", "symfony/dotenv": "5.2.*", "symfony/expression-language": "5.2.*", "symfony/flex": "^1.3.1", "symfony/framework-bundle": "5.2.*", "symfony/http-client": "5.2.*", "symfony/mime": "5.2.*", "symfony/monolog-bundle": "^3.5", "symfony/proxy-manager-bridge": "5.2.*", "symfony/security-bundle": "5.2.*", "symfony/security-core": "5.2.*", "symfony/webpack-encore-bundle": "^1.11", "symfony/yaml": "5.2.*", "twig/extra-bundle": "^3.3", "twig/intl-extra": "^3.3"}, "require-dev": {"symfony/maker-bundle": "^1.26", "symfony/phpunit-bridge": "^5.2", "symfony/profiler-pack": "^1.0"}, "config": {"optimize-autoloader": true, "preferred-install": {"*": "dist"}, "sort-packages": true}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/"}}, "replace": {"paragonie/random_compat": "2.*", "symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php72": "*", "symfony/polyfill-php71": "*", "symfony/polyfill-php70": "*", "symfony/polyfill-php56": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"]}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "require": "5.2.*"}}}