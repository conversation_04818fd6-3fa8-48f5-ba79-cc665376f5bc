<?php

declare(strict_types=1);

namespace App\Entity;

use DateTime;
use Doctrine\ORM\Mapping as ORM;
use App\Repository\CountRepository;
use ApiPlatform\Core\Annotation\ApiResource;
use Symfony\Component\Serializer\Annotation\Groups;
use Symfony\Component\Validator\Constraints as Assert;

/**
 * @ApiResource(
 *     formats={"json", "jsonld"},
 *     attributes={"pagination_client_items_per_page"=true},
 *     itemOperations={
 *        "get"={
 *            "method": "GET",
 *            "path"="/counts/{id}",
 *            "normalization_context"={"groups"={"countRead"}},
 *        },
 *        "patch"={
 *            "method": "PATCH",
 *            "path"="/counts/{id}",
 *            "normalization_context"={"groups"={"countReadPatch"}},
 *            "denormalization_context"={"groups"={"countWritePatch"}},
 *        },
 *     },
 *     collectionOperations={
 * 		  "post"={
 *            "normalization_context"={"groups"={"countRead"}},
 *            "denormalization_context"={"groups"={"countWrite"}},
 *        },
 *        "get"={
 *            "normalization_context"={"groups"={"countRead"}},
 *        },
 * 	   },
 * )
 *
 * @ORM\Entity(repositoryClass=CountRepository::class)
 * @ORM\Table(name="counts")
 */
class Count
{
	/**
	 * @ORM\Id()
	 * @ORM\GeneratedValue()
	 * @ORM\Column(type="integer")
	 * @Groups({"countRead", "countReadPatch"})
	 */
	private int $id;

	/**
	 * @ORM\Column(name="created_date", type="datetime")
	 * @Groups({"countRead"})
	 */
	private DateTime $createdDate;

	/**
	 * @ORM\Column(name="product_id", type="integer")
	 * @Groups({"countRead","countWrite"})
	 */
	private int $productId;

	/**
	 * @ORM\Column(name="product_name", type="string", length=40)
	 * @Groups({"countRead"})
	 */
	private string $productName;

	/**
	 * @ORM\Column(name="product_id_sf", type="string", length=40)
	 * @Groups({"countRead"})
	 */
	private string $productIdSF;

	/**
	 * @ORM\Column(name="broadcast_medium", type="json_array", length=40)
	 * @Assert\Choice(choices = {"seloger", "logicimmo"}, multiple=true)
	 * @Groups({"countRead","countWrite"})
	 * @Assert\NotBlank()
	 */
	private array $broadcastMedium;

	/**
	 * @ORM\Column(name="project_type", type="json_array", length=40)
	 * @Assert\Choice(choices = {"Achat", "Location"}, multiple=true)
	 * @Groups({"countRead","countWrite"})
	 */
	private array $projectType;

	/**
	 * @ORM\Column(name="periodicity", type="string", length=40)
     * @Assert\Choice(choices = {"Récurrent", "Oneshot"})
	 * @Groups({"countRead","countWrite"})
	 * @Assert\NotBlank()
	 */
	private string $periodicity;

	/**
	 * @ORM\Column(name="start_date", type="datetime")
	 * @Groups({"countRead","countWrite"})
	 * @Assert\NotBlank()
	 */
	private DateTime $startDate;

	/**
	 * @ORM\Column(name="end_date", type="datetime")
	 * @Groups({"countRead","countWrite"})
	 * @Assert\NotBlank()
	 */
	private DateTime $endDate;

    /**
     * @ORM\Column(name="locality", type="json_array")
     * @Groups({"countRead","countWrite"})
     * @Assert\NotBlank()
     */
	private array $locality;

	/**
	 * @ORM\Column(name="count_item", type="json_array")
	 * @Groups({"countRead","countWrite"})
	 * @Assert\NotBlank()
	 */
	private array $countItem;

	/**
	 * @ORM\Column(name="user_id", type="integer")
	 * @Groups({"countRead"})
	 */
	private int $userId;

	/**
	 * @ORM\Column(name="market", type="string", length=40)
	 * @Assert\Choice(choices = {"ancien", "neuf", "construire", "luxe", "bureauxEtCommerces"})
	 * @Groups({"countRead","countWrite"})
	 * @Assert\NotBlank()
	 */
	private string $market;

	/**
	 * @ORM\Column(name="client_id", type="string", length=40)
     * @Groups({"countRead","countWrite"})
	 */
	private ?string $clientId;

	/**
	 * @ORM\Column(name="total_imp_purchased", type="integer")
	 * @Groups({"countRead","countWrite"})
	 * @Assert\NotBlank()
	 */
	private int $totalImpPurchased;

	/**
	 * @ORM\Column(name="total_budget", type="integer")
	 * @Groups({"countRead","countWrite"})
	 * @Assert\NotBlank()
	 */
	private int $totalBudget;

    /**
     * @ORM\Column(name="status", type="string")
     * @Groups({"countReadPatch", "countWritePatch"})
     * @Assert\Choice(choices = {"archived"})
     */
	private ?string $status;

	/**
	 * Get the value of id
	 * @return int
	 */
	public function getId(): int
	{
		return $this->id;
	}

	/**
	 * Set the value of id
	 * @param int $id
	 */
	public function setId(int $id): void
	{
		$this->id = $id;
	}

	/**
	 * Get the value of createdDate
	 * @return DateTime
	 */
	public function getCreatedDate(): DateTime
	{
		return $this->createdDate;
	}

	/**
	 * Set the value of createdDate
	 * @param DateTime $createdDate
	 */
	public function setCreatedDate(DateTime $createdDate): void
	{
		$this->createdDate = $createdDate;
	}

	/**
	 * Get the value of broadcastMedium
	 * @return array
	 */
	public function getBroadcastMedium(): array
	{
		return $this->broadcastMedium;
	}

	/**
	 * Set the value of broadcastMedium
	 * @param array $broadcastMedium
	 */
	public function setBroadcastMedium(array $broadcastMedium): void
	{
		$this->broadcastMedium = $broadcastMedium;
	}

	/**
	 * Get the value of periodicity
	 * @return string
	 */
	public function getPeriodicity(): string
	{
		return $this->periodicity;
	}

	/**
	 * Set the value of periodicity
	 * @param string $periodicity
	 */
	public function setPeriodicity(string $periodicity): void
	{
		$this->periodicity = $periodicity;
	}

	/**
	 * Get the value of startDate
	 * @return DateTime
	 */
	public function getStartDate(): DateTime
	{
		return $this->startDate;
	}

	/**
	 * Set the value of startDate
	 * @param DateTime $startDate
	 */
	public function setStartDate(DateTime $startDate): void
	{
		$this->startDate = $startDate;
	}

	/**
	 * Get the value of endDate
	 * @return DateTime
	 */
	public function getEndDate(): DateTime
	{
		return $this->endDate;
	}

	/**
	 * Set the value of endDate
	 * @param DateTime $endDate
	 */
	public function setEndDate(DateTime $endDate): void
	{
		$this->endDate = $endDate;
	}

	/**
	 * Get the value of market
	 * @return string
	 */
	public function getMarket(): string
	{
		return $this->market;
	}

	/**
	 * Set the value of market
	 * @param string $market
	 */
	public function setMarket(string $market): void
	{
		$this->market = $market;
	}

	/**
	 * Get the value of productName
	 * @return string
	 */
	public function getProductName(): string
	{
		return $this->productName;
	}

	/**
	 * Set the value of productName
	 * @param string $productName
	 */
	public function setProductName(string $productName): void
	{
		$this->productName = $productName;
	}

	/**
	 * Get the value of userId
	 * @return int
	 */
	public function getUserId(): int
	{
		return $this->userId;
	}

	/**
	 * Set the value of userId
	 * @param int $userId
	 */
	public function setUserId(int $userId): void
	{
		$this->userId = $userId;
	}

	/**
	 * Get the value of projectType
	 * @return array
	 */
	public function getProjectType(): array
	{
		return $this->projectType;
	}

	/**
	 * Set the value of projectType
	 * @param array $projectType
	 */
	public function setProjectType(array $projectType): void
	{
		$this->projectType = $projectType;
	}

	/**
	 * Get the value of clientId
	 * @return ?string
	 */
	public function getClientId(): ?string
	{
		return $this->clientId;
	}

	/**
	 * Set the value of clientId
	 * @param string $clientId
	 */
	public function setClientId(string $clientId): void
	{
		$this->clientId = $clientId;
	}

	/**
	 * Get the value of totalImpPurchased
	 * @return int
	 */
	public function getTotalImpPurchased(): int
	{
		return $this->totalImpPurchased;
	}

	/**
	 * Set the value of totalImpPurchased
	 * @param int $totalImpPurchased
	 */
	public function setTotalImpPurchased(int $totalImpPurchased): void
	{
		$this->totalImpPurchased = $totalImpPurchased;
	}

	/**
	 * Get the value of totalBudget
	 * @return int
	 */
	public function getTotalBudget(): int
	{
		return $this->totalBudget;
	}

	/**
	 * Set the value of totalBudget
	 * @param int $totalBudget
	 */
	public function setTotalBudget(int $totalBudget): void
	{
		$this->totalBudget = $totalBudget;
	}

	/**
	 * Get the value of productId
	 * @return int
	 */
	public function getProductId(): int
	{
		return $this->productId;
	}

	/**
	 * Set the value of productId
	 * @param int $productId
	 */
	public function setProductId(int $productId): void
	{
		$this->productId = $productId;
	}

	/**
	 * Get the value of productIdSF
	 * @return string
	 */
	public function getProductIdSF(): string
	{
		return $this->productIdSF;
	}

	/**
	 * Set the value of productIdSF
	 * @param string $productIdSF
	 */
	public function setProductIdSF(string $productIdSF): void
	{
		$this->productIdSF = $productIdSF;
	}

	/**
	 * Get the value of countItem
	 * @return array
	 */
	public function getCountItem(): array
	{
		return $this->countItem;
	}

	/**
	 * Set the value of countItem
	 * @param array $countItem
	 */
	public function setCountItem(array $countItem): void
	{
		$this->countItem = $countItem;
	}

    /**
     * @return array
     */
    public function getLocality(): array
    {
        return $this->locality;
    }

    /**
     * @param array $locality
     */
    public function setLocality(array $locality): void
    {
        $this->locality = $locality;
    }

    /**
     * @return ?string
     */
    public function getStatus(): ?string
    {
        return $this->status;
    }

    /**
     * @param string $status
     */
    public function setStatus(string $status): void
    {
        $this->status = $status;
    }
}
