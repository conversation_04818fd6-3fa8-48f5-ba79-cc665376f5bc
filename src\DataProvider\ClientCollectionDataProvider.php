<?php

namespace App\DataProvider;

use ApiPlatform\Core\DataProvider\CollectionDataProviderInterface;
use ApiPlatform\Core\DataProvider\RestrictedDataProviderInterface;
use ApiPlatform\Core\Exception\RuntimeException;
use Symfony\Component\HttpFoundation\RequestStack;
use App\Entity\Client;
use App\Hexagonal\Client\Domain\ClientRepositoryPort;

final class ClientCollectionDataProvider implements CollectionDataProviderInterface, RestrictedDataProviderInterface
{
    private ClientRepositoryPort $clientRepository;
    private RequestStack $requestStack;

    public function __construct(ClientRepositoryPort $clientRepository, RequestStack $requestStack)
    {
        $this->clientRepository = $clientRepository;
        $this->requestStack = $requestStack;
    }

    public function supports(string $resourceClass, string $operationName = null, array $context = []): bool
    {
        return Client::class === $resourceClass;
    }

    public function getCollection(string $resourceClass, string $operationName = null): \Generator
    {
        $clientId = $this->requestStack->getCurrentRequest()->get('clientId');

        if (!$clientId) {
            throw new RuntimeException('The api call must have the clientId parameter set.');
        }

        $results = $this->clientRepository->getAllByClientId($clientId);

        foreach ($results as $result) {
            yield new Client($result->getId(), $result->getClientId(), $result->getIntensity());
        }
    }
}
