<?php

declare(strict_types=1);

namespace App\Hexagonal\Forecast\Adapters;

use App\Hexagonal\Forecast\Domain\Service\ForecastPort;

class ForecastApiService
{
    private ForecastPort $forecastPort;

    public function __construct(ForecastPort $forecastPort)
    {
        $this->forecastPort = $forecastPort;
    }

    public function get(ForecastApiRequest $forecastApiRequest): array
    {
        return $this->forecastPort->get(
            $forecastApiRequest->getProductId(),
            $forecastApiRequest->getPostalCode(),
            $forecastApiRequest->getBroadcastMedium(),
            $forecastApiRequest->getProjectType(),
            $forecastApiRequest->getStartDate(),
            $forecastApiRequest->getEndDate()
        );
    }
}