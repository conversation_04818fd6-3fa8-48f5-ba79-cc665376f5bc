<?php
/**
 * Created by PhpStorm.
 * User: 924871
 * Date: 01/03/2021
 * Time: 15:44
 */

declare(strict_types=1);

namespace App\Hexagonal\Security\Adapters\JWT\TokenAuthenticator;

use App\Entity\User;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Exception\LogicException;
use Symfony\Component\Security\Core\Exception\AuthenticationException;
use Symfony\Component\Security\Core\Exception\CustomUserMessageAuthenticationException;
use Symfony\Component\Security\Http\Authenticator\AuthenticatorInterface;
use Symfony\Component\Security\Http\Authenticator\Passport\Badge\UserBadge;
use Symfony\Component\Security\Http\Authenticator\Passport\PassportInterface;
use Symfony\Component\Security\Http\Authenticator\Passport\SelfValidatingPassport;
use Symfony\Component\Security\Http\Authenticator\Passport\UserPassportInterface;
use Symfony\Component\Security\Http\Authenticator\Token\PostAuthenticationToken;
use App\Hexagonal\Security\Domain\JWT\TokenExtractor\TokenExtractorPort;
use App\Hexagonal\Security\Domain\JWT\TokenManager\TokenManagerPort;
use App\Hexagonal\Security\Domain\PublicKeyManager\PublicKeyManagerPort;
use App\Hexagonal\User\Domain\AuthenticateUserPort;

final class JWTTokenAuthenticator implements AuthenticatorInterface
{
    private TokenManagerPort $tokenManager;
    private TokenExtractorPort $tokenExtractor;
    private AuthenticateUserPort $authenticateUser;
    private PublicKeyManagerPort $publicKeyManager;

    public function __construct(
        TokenManagerPort $tokenManager,
        TokenExtractorPort $tokenExtractor,
        AuthenticateUserPort $authenticateUser,
        PublicKeyManagerPort $publicKeyManager
    ) {
        $this->tokenManager = $tokenManager;
        $this->tokenExtractor = $tokenExtractor;
        $this->authenticateUser = $authenticateUser;
        $this->publicKeyManager = $publicKeyManager;
    }

    public function supports(Request $request): ?bool
    {
        return false !== $this->tokenExtractor->extract($request);
    }

    /**
     * Entry point for authorization checks
     * @inheritdoc
     */
    public function authenticate(Request $request): PassportInterface
    {
        $jwt = $this->tokenExtractor->extract($request);
        if (null === $jwt) {
            // The token header was empty, authentication fails with HTTP Status
            // Code 401 "Unauthorized"
            throw new CustomUserMessageAuthenticationException('No API token provided');
        }

        // Retrieve the kid of the token
        $kid = $this->tokenManager->getKid($jwt);
        if(null === $kid){
            throw new CustomUserMessageAuthenticationException('Authorization has been denied for this request');
        }

        // Fetch the public key by kid
        $key = $this->publicKeyManager->fetch($kid);
        if(null === $key){
            throw new CustomUserMessageAuthenticationException('Authorization has been denied for this request');
        }

        // Verify the token with public key
        $decoded = $this->tokenManager->decode($jwt, $key, ['RS256']);

        // add user to DB if not found
        $this->authenticateUser->process(new User($decoded->email, $decoded->given_name, $decoded->family_name));

        return new SelfValidatingPassport(new UserBadge($decoded->email));
    }

    public function createAuthenticatedToken(PassportInterface $passport, string $firewallName): TokenInterface
    {
        if (!$passport instanceof UserPassportInterface) {
            throw new LogicException(sprintf('Passport does not contain a user, overwrite "createAuthenticatedToken()" in "%s" to create a custom authenticated token.', static::class));
        }

        return new PostAuthenticationToken($passport->getUser(), $firewallName, $passport->getUser()->getRoles());
    }

    public function onAuthenticationSuccess(Request $request, TokenInterface $token, string $firewallName): ?Response
    {
        // on success, let the request continue
        return null;
    }

    public function onAuthenticationFailure(Request $request, AuthenticationException $exception): ?Response
    {
        $data = [
            'message' => strtr($exception->getMessageKey(), $exception->getMessageData())
        ];

        return new JsonResponse($data, Response::HTTP_UNAUTHORIZED);
    }
}
