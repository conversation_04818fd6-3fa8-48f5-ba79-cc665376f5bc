<?php

declare(strict_types=1);

namespace App\Entity;

use ApiPlatform\Core\Annotation\ApiResource;
use ApiPlatform\Core\Annotation\ApiProperty;
use ApiPlatform\Core\Action\NotFoundAction;

/**
 * Return a Forecast by code (dpt or code), with data provided from Google/Smart Adserver
 *
 * @ApiResource(
 *     formats={"json"},
 *     collectionOperations={"get"},
 *     itemOperations={
 *         "get"={
 *             "controller"=NotFoundAction::class,
 *             "read"=false,
 *             "output"=false,
 *         },
 *     },
 * )
 */
class Forecast
{
    private string $code;
    private string $name;

    /**
     * @ApiProperty(identifier=true)
     */
    private int $available;

    private int $sold;
    private int $total;

    /**
     * @param int $total
     * @param int $available
     * @param int $sold
     * @param string $code
     * @param string $name
     */
    public function __construct(int $total, int $available, int $sold, string $code, string $name)
    {
        $this->total = $total;
        $this->available = $available;
        $this->sold = $sold;
        $this->code = $code;
        $this->name = $name;
    }

    public function getCode(): string
    {
        return $this->code;
    }

    public function getAvailable(): int
    {
        return $this->available;
    }

    public function getSold(): int
    {
        return $this->sold;
    }

    public function getTotal(): int
    {
        return $this->total;
    }

    public function getName(): string
    {
        return $this->name;
    }
}
