<?php

declare(strict_types=1);

namespace App\DataPersister;

use DateTime;
use DateTimeZone;
use App\Entity\User;
use App\Entity\Count;
use App\Entity\Product;
use Symfony\Component\Security\Core\Security;
use ApiPlatform\Core\Exception\RuntimeException;
use App\Hexagonal\Count\Domain\Database\CountRepositoryPort;
use App\Hexagonal\Product\Domain\Database\ProductRepositoryPort;
use App\Hexagonal\Count\Domain\Validator\CountValidatorPort;
use ApiPlatform\Core\DataPersister\ContextAwareDataPersisterInterface;

final class CountDataPersister implements ContextAwareDataPersisterInterface
{
    private CountValidatorPort $countValidator;
    private Security $security;
    private CountRepositoryPort $countRepository;
    private ProductRepositoryPort $productRepository;
    private const MASTERDISPLAY = [
        'Récurrent' => [
            ["keyName" => "selogerHabillageRecurrent", "productName" => "Seloger Habillage Récurrent"],
            ["keyName" => "masterDisplay", "productName" => "Master Display"]
        ],
        'Oneshot' => [
            ["keyName" => "selogerHabillageOneShot", "productName" => "Seloger Habillage One Shot"],
            ["keyName" => "masterDisplayOneShot", "productName" => "Master Display One Shot"]
        ]
    ];

    public function __construct(ProductRepositoryPort $productRepository,
                                CountRepositoryPort $countRepository,
                                CountValidatorPort $countValidator,
                                Security $security)
    {
        $this->countRepository = $countRepository;
        $this->productRepository = $productRepository;
        $this->countValidator = $countValidator;
        $this->security = $security;
    }

    public function supports($data, array $context = []): bool
    {
        return $data instanceof Count;
    }

    public function persist($count, array $context = [])
    {
        $currentUser = $this->security->getUser();

        // PATCH
        if (isset($context['item_operation_name']) && $context['item_operation_name'] === 'patch') {
            if (!($this->countValidator->isPatchValid($count, $currentUser))) {
                throw new RuntimeException($this->countValidator->getErrorMessage());
            }

            $this->countRepository->update($count);
            return;
        }

        // POST
        if (!($this->countValidator->isPostValid($count, $currentUser))) {
            throw new RuntimeException($this->countValidator->getErrorMessage());
        }

        $productParams = $this->getProductParams($count->getProductId());
        $this->setDataByProductId($productParams, $count, $currentUser);
        $this->countRepository->create($count);
    }

    public function remove($data, array $context = [])
    {
        // call your persistence layer to delete $data
    }

    /**
     * Get Product params from 'product_adfactory' by product id
     *
     * @param int $productId
     * @return Product $productParams
     */
    private function getProductParams(int $productId): Product
    {
        return $this->productRepository->get($productId);
    }

    /**
     * @param Product $product
     * @param Count $count
     * @param User $user
     * @throws \Exception
     */
    private function setDataMasterDisplay(Product $product, Count $count, User $user): void
    {
        $aIdProductSf = json_decode($product->getProductIdSF(), true);
        $indexBroadcastMedium = count($count->getBroadcastMedium()) - 1;
        $keyName = self::MASTERDISPLAY[$count->getPeriodicity()][$indexBroadcastMedium]['keyName'];
        $productName = self::MASTERDISPLAY[$count->getPeriodicity()][$indexBroadcastMedium]['productName'];

        $count->setProductIdSF($aIdProductSf[$keyName]);
        $count->setProductName($productName);
        $count->setCreatedDate(new DateTime('now', new DateTimeZone('utc')));
        $count->setUserId($user->getId());
        $count->setClientId('');
    }

    /**
     * @param Product $product
     * @param Count $count
     * @param User $user
     * @throws \Exception
     */
    private function setDataExpert360(Product $product, Count $count, User $user): void
    {
        $productName = 'Expert 360°';

        $count->setProductIdSF('');
        $count->setProductName($productName);
        $count->setCreatedDate(new DateTime('now', new DateTimeZone('utc')));
        $count->setUserId($user->getId());
    }

    /**
     * @param Product $product
     * @param Count $count
     * @param User $user
     * @throws \Exception
     */
    private function setDataByProductId(Product $product, Count $count, User $user): void
    {
        $productId = $count->getProductId();
        if ($productId === 1) {
            $this->setDataMasterDisplay($product, $count, $user);
        } elseif ($productId === 2) {
            $this->setDataExpert360($product, $count, $user);
        }
    }
}
