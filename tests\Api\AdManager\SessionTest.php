<?php

namespace App\Tests\Api\AdManager;

use App\Api\AdManager\SessionService;
use PHPUnit\Framework\TestCase;

class SessionTest extends TestCase
{
    public function testSetSession()
    {
        $adManagerConfigPath = getenv('ADMANAGER_INI');
        $session = new SessionService($adManagerConfigPath);
        $applicationName = $session->getSession()->getApplicationName();

        $this->assertEquals('myadfactory', $applicationName);
    }
}
