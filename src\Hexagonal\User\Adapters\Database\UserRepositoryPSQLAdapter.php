<?php

declare(strict_types=1);

namespace App\Hexagonal\User\Adapters\Database;

use Doctrine\ORM\EntityManagerInterface;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepositoryInterface;
use Symfony\Component\Security\Core\User\UserInterface;
use App\Hexagonal\User\Domain\Database\UserRepositoryPort;
use App\Entity\User;

final class UserRepositoryPSQLAdapter implements UserRepositoryPort
{
    private EntityManagerInterface $entityManager;
    private ServiceEntityRepositoryInterface $repository;

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
        $this->repository = $this->entityManager->getRepository(User::class);
    }

    public function create(UserInterface $user): UserInterface
    {
        $this->entityManager->persist($user);
        $this->entityManager->flush();

        return $user;
    }

    public function getByEmail(string $email): ?UserInterface
    {
        return $this->repository->findOneBy(['email' => $email]);
    }

    public function get(int $id): ?UserInterface
    {
        return $this->repository->findOneBy(['id' => $id]);
    }

    public function getAll(): array
    {
        return $this->repository->findAll();
    }

    public function update(UserInterface $user): ?UserInterface
    {
        $userToUpdate = $this->repository->findOneBy(['id' => $user->getId()]);

        if (!$userToUpdate) {
            $this->entityManager->persist($userToUpdate);
        }

        $this->entityManager->flush();

        return $userToUpdate;
    }
}
