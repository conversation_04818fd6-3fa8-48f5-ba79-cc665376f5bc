.contentSummary {
  position: relative;
  padding: 1.1em 1.375em;
  border-radius: 0.3em;
  max-width: 50em;
  width: 49%;
  background: $white;
  box-shadow: 0 0.1em 0.5em 0 rgba(0, 0, 0, 0.16);
  top: 1em;

  .broadcastMedium {
    display: flex;
    position: absolute;
    top: -1.5em;

    .medium {
      &:first-child {
        margin-left: 0;
      }

      background-size: 90%;
      margin-left: 1em;
      background-color: $white;
      background-repeat: no-repeat;
      background-position: center;
      height: 4.75em;
      width: 4.75em;
      border-radius: 0.3em;
      box-shadow: 0 0.125em 0.5em 0 rgba(0, 0, 0, 0.16);
    }
  }

  .loadingBarWrapper {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    height: 3.4em;

    .BarBudget {
      width: 3.063em;
      height: 0.875em;
      border-radius: 0.25em;
      margin-bottom: 0.25em;
    }

    .BarPrints {
      width: 4.938em;
      height: 0.75em;
      border-radius: 0.25em;
      margin-bottom: 0.25em;
    }
  }

  .BarPrintPerMonths {
    width: 4.438em;
    height: 0.75em;
    border-radius: 0.25em;
  }

  .BarBudget,
  .BarPrints,
  .BarPrintPerMonths {
    animation-duration: 1s;
    animation-fill-mode: forwards;
    animation-iteration-count: infinite;
    animation-name: lazyloadCountResult;
    animation-timing-function: linear;
    background: $lighterSaphir;
    background-size: 4.938em 0.75em;
    background-image: linear-gradient(
                    to right,
                    $lighterSaphir 0%,
                    #f7f8ff 20%,
                    $lighterSaphir 40%,
                    $lighterSaphir 100%
    );
  }

  .priceAndPrinting {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    height: 3.4em;

    .price {
      font-size: 1.125em;
      font-family: $font-sourceSansPro;
      font-weight: 700;
      color: $black;
    }

    .printing {
      width: 30%;
      font-size: 0.75em;
      font-family: $font-sourceSansPro;
      color: $black;
      text-align: right;
    }
  }

  .productName {
    margin-top: 0.3em;
    font-family: $font-sourceSansPro;
    font-weight: 700;
    color: $black;
    font-size: 1.5em;
  }

  .tableInformations {
    margin-top: 1.5em;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-auto-rows: minmax(4.25em, auto);
    border-bottom: 1px solid $greyTable;
    border-left: 1px solid $greyTable;
    border-top-left-radius: 0.5em;
    border-top-right-radius: 0.5em;
    border-bottom-left-radius: 0.5em;
    border-bottom-right-radius: 0.5em;

    > div {
      display: flex;
      text-align: center;
      border-top: 1px solid $greyTable;
      border-right: 1px solid $greyTable;
      align-items: center;
      padding: 0.5em;

      .icon {
        height: 1.4em;
        width: 20%;
        margin-right: 0.5em;

        @include middleDesktop() {
          margin-right: 0;
          margin-bottom: 0.1em;
        }
      }

      .contentBloc {
        display: flex;
        flex-direction: column;
        text-align: left;
        width: 80%;

        .title {
          font-size: 1.125em;
          font-family: $font-sourceSansPro;
          font-weight: 700;
          color: $black;
          margin-bottom: 0.1em;
          @include middleDesktop() {
            font-size: 0.875em;
            text-align: center;
          }
        }

        .description {
          font-size: 0.75em;
          font-family: $font-sourceSansPro;
          color: $black;

          @include middleDesktop() {
            text-align: center;
          }
        }
      }
    }

    .display {
      border-top-left-radius: 0.5em;
      border-bottom-left-radius: 0.5em;
      grid-column: 1;
      grid-row: 1;
      background-color: $white;

      @include middleDesktop() {
        display: flex;
        flex-direction: column;
      }
    }

    .projectType {
      grid-column: 2;
      grid-row: 1;
      background-color: $white;

      @include middleDesktop() {
        display: flex;
        flex-direction: column;
      }
    }

    .reccuring {
      border-top-right-radius: 0.5em;
      border-bottom-right-radius: 0.5em;
      grid-column: 3;
      grid-row: 1;
      background-color: $white;

      @include middleDesktop() {
        display: flex;
        flex-direction: column;
      }
    }
  }

  .location {
    margin-top: 1.5em;

    .locationTitle {
      font-size: 1.125em;
      color: $black;
      font-family: $font-sourceSansPro;
      font-weight: 700;
    }

    .contentLocation {
      max-height: 14.8em;
      overflow: auto;
      margin-top: 0.8em;
      display: flex;
      flex-wrap: wrap;

      > div {
        margin-right: 0.5em;
        margin-bottom: 0.5em;
        background-color: $lighterSaphir;
        padding: 0 0.5em 0.1em 0.5em;
        color: $saphir;
        border-radius: 0.3em;

        span {
          margin: 0;
          font-size: 0.875em;
          font-weight: 600;
        }
      }

      @include middleDesktop {
        max-height: 12em;
      }
    }
  }
}
