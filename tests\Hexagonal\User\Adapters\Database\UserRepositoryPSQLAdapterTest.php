<?php

namespace App\Tests\Hexagonal\User\Adapters\Database;

use PHPUnit\Framework\TestCase;
use Doctrine\ORM\EntityManagerInterface;
use App\Hexagonal\User\Adapters\Database\UserRepositoryPSQLAdapter;
use App\Hexagonal\User\Domain\Database\UserRepositoryPort;
use App\Entity\User;
use App\Repository\UserRepository;

class UserRepositoryPSQLAdapterTest extends TestCase
{
    private ?UserRepository $userRepoMock;
    private ?EntityManagerInterface $entityManagerMock;

    protected function setUp()
    {
        $this->userRepoMock = $this->createMock(UserRepository::class);
        $this->entityManagerMock = $this->createMock(EntityManagerInterface::class);
    }

    protected function tearDown()
    {
        $this->userRepoMock = null;
        $this->entityManagerMock = null;
    }

    public function testCreate()
    {
        // arrange
        $expected = 4;
        $user = new User(null, '<EMAIL>', 'firstNameUser4', 'nameUser4');

        $this->entityManagerMock
            ->expects($this->any())
            ->method('getRepository')
            ->will($this->returnValue($this->userRepoMock));

        $this->entityManagerMock
            ->expects($this->any())
            ->method('persist')
            ->will($this->returnCallback(function ($o) use ($expected) {
                if ($o instanceof User) {
                    $o->setId($expected);
                }
            }));

        $this->userRepository = new UserRepositoryPSQLAdapter($this->entityManagerMock);

        // act
        $createdUser = $this->userRepository->create($user);

        // assert
        $this->assertEquals($expected, $createdUser->getId());
    }

    public function testGetByEmail()
    {
        // arrange
        $expected = '<EMAIL>';
        $user = new User(4, '<EMAIL>', 'firstNameUser4', 'nameUser4');

        $this->userRepoMock
            ->expects($this->any())
            ->method('findOneBy')
            ->with(['email' => $user->getEmail()])
            ->will($this->returnValue($user));

        $this->entityManagerMock
            ->expects($this->any())
            ->method('getRepository')
            ->with(User::class)
            ->will($this->returnValue($this->userRepoMock));

        $userRepository = new UserRepositoryPSQLAdapter($this->entityManagerMock);

        // act
        $user = $userRepository->getByEmail($expected);

        // assert
        $this->assertEquals($expected, $user->getEmail());
    }

    public function testGet()
    {
        // arrange
        $expected = 3;
        $user = new User(3, '<EMAIL>', 'firstNameUser4', 'nameUser4');

        $this->userRepoMock
            ->expects($this->any())
            ->method('findOneBy')
            ->with(['id' => $user->getId()])
            ->will($this->returnValue($user));

        $this->entityManagerMock
            ->expects($this->any())
            ->method('getRepository')
            ->with(User::class)
            ->will($this->returnValue($this->userRepoMock));

        $userRepository = new UserRepositoryPSQLAdapter($this->entityManagerMock);

        // act
        $user = $userRepository->get($expected);

        // assert
        $this->assertEquals($expected, $user->getId());
    }

    public function testGetAll()
    {
        // arrange
        $expected = 3;
        $user1 = new User(1, '<EMAIL>', 'firstNameUser1', 'nameUser1');
        $user2 = new User(2, '<EMAIL>', 'firstNameUser2', 'nameUser2');
        $user3 = new User(3, '<EMAIL>', 'Gon3', 'Freecs3');
        $users = [$user1, $user2, $user3];

        $this->userRepoMock
            ->expects($this->any())
            ->method('findAll')
            ->will($this->returnValue($users));

        $this->entityManagerMock
            ->expects($this->any())
            ->method('getRepository')
            ->with(User::class)
            ->will($this->returnValue($this->userRepoMock));

        $userRepository = new UserRepositoryPSQLAdapter($this->entityManagerMock);

        // act
        $users = $userRepository->getAll();

        // assert
        $this->assertCount($expected, $users);
    }

    public function testUpdate()
    {
        // arrange
        $user = new User(3, '<EMAIL>', 'Gon3', 'Freecs3');
        $expected = ['ancien'];

        $this->userRepoMock
            ->expects($this->at(0))
            ->method('findOneBy')
            ->with(['id' => $user->getId()])
            ->will($this->returnValue($user));

        $this->entityManagerMock
            ->expects($this->any())
            ->method('getRepository')
            ->with(User::class)
            ->will($this->returnValue($this->userRepoMock));

        $this->entityManagerMock
            ->expects($this->any())
            ->method('flush')
            ->will($this->returnCallback(function () use ($user) {
                $user->setMarket(['ancien']);
            }));

        $userRepository = new UserRepositoryPSQLAdapter($this->entityManagerMock);

        // act
        $updatedUser = $userRepository->update($user);

        // assert
        $this->assertEquals($expected, $updatedUser->getMarket());
    }
}
