<?php
/**
 * Created by PhpStorm.
 * User: 924871
 * Date: 09/03/2021
 * Time: 14:49
 */

namespace App\DataProvider;

use ApiPlatform\Core\DataProvider\ItemDataProviderInterface;
use ApiPlatform\Core\DataProvider\RestrictedDataProviderInterface;
use ApiPlatform\Core\Exception\RuntimeException;
use Symfony\Component\Security\Core\Security;
use Symfony\Component\HttpFoundation\RequestStack;
use App\Hexagonal\User\Domain\Database\UserRepositoryPort;
use App\Entity\User;

final class UserItemDataProvider implements ItemDataProviderInterface, RestrictedDataProviderInterface
{
    private RequestStack $requestStack;
    private UserRepositoryPort $userRepository;
    private Security $security;

    public function __construct(RequestStack $requestStack, UserRepositoryPort $userRepository, Security $security)
    {
        $this->requestStack = $requestStack;
        $this->userRepository = $userRepository;
        $this->security = $security;
    }

    public function supports(string $resourceClass, string $operationName = null, array $context = []): bool
    {
        return User::class === $resourceClass;
    }

    public function getItem(string $resourceClass, $id, string $operationName = null, array $context = []): ?User
    {
        $identifier = $this->requestStack->getCurrentRequest()->attributes->get('id');
        $currentUser = $this->security->getUser();

        // return self
        if ($identifier === 'me') {
            return $this->userRepository->get($currentUser->getId());
        }

        // only int value or self
        if (intval($identifier) <= 0 ||
            (($currentUser->getId() !== intval($identifier)) && (!in_array('ROLE_ADMIN', $currentUser->getRoles()))
        )) {
            throw new RuntimeException('Access Denied.');
        }

        return $this->userRepository->get($identifier);
    }
}
