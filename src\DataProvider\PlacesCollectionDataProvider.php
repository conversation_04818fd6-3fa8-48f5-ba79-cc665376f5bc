<?php

namespace App\DataProvider;

use ApiPlatform\Core\DataProvider\CollectionDataProviderInterface;
use ApiPlatform\Core\DataProvider\RestrictedDataProviderInterface;
use ApiPlatform\Core\Exception\ResourceClassNotSupportedException;
use ApiPlatform\Core\Exception\RuntimeException;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Component\HttpFoundation\RequestStack;
use App\Entity\Place;
use App\Entity\Department;
use App\Entity\ZipCity;

final class PlacesCollectionDataProvider implements CollectionDataProviderInterface, RestrictedDataProviderInterface
{
    private ManagerRegistry $managerRegistry;
    private RequestStack $requestStack;

    public function __construct(ManagerRegistry $managerRegistry, RequestStack $requestStack)
    {
        $this->managerRegistry = $managerRegistry;
        $this->requestStack = $requestStack;
    }

    public function supports(string $resourceClass, string $operationName = null, array $context = []): bool
    {
        return Place::class === $resourceClass;
    }

    public function getCollection(string $resourceClass, string $operationName = null): \Generator
    {
        $query = $this->requestStack->getCurrentRequest()->get('name');

        if (!$query){
            throw new RuntimeException('The api call must have the name parameter set.');
        }

        $departmentRepository = $this->managerRegistry->getRepository(Department::class);
        $resDpt = $departmentRepository->findByNameOrCode($query);

        $cityRepository = $this->managerRegistry->getRepository(ZipCity::class);
        $resCity = $cityRepository->findByNameOrCode($query);

        foreach($resDpt as $res){
            yield new Place($res->getId(), $res->getCode(), $res->getName());
        }
        foreach($resCity as $res){
            yield new Place($res->getId(), $res->getCode(), $res->getName());
        }
    }
}
