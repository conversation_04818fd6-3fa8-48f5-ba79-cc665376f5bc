<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass="App\Repository\DepartmentRepository")
 * @ORM\Table(name="ref_department")
 */
class Department
{
    /**
     * @var int
     *
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @var string department name
     *
     * @ORM\Column(name="name", type="string")
     */
    private $name;

    /**
     * @var string department name (light)
     *
     * @ORM\Column(name="name_light", type="string")
     */
    private $nameLight;

    /**
     * @var string department code
     *
     * @ORM\Column(name="code", type="string")
     */
    private $code;

    /**
     * @var string id admanager
     *
     * @ORM\Column(name="id_admanager", type="string")
     */
    private $admanagerId;

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @return string
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * @return string
     */
    public function getCode(): string
    {
        return $this->code;
    }

    /**
     * Get id admanager
     *
     * @return  string
     */ 
    public function getAdmanagerId()
    {
        return $this->admanagerId;
    }
}
