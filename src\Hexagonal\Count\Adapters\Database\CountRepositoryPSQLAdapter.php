<?php

declare(strict_types=1);

namespace App\Hexagonal\Count\Adapters\Database;

use Doctrine\ORM\EntityManagerInterface;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepositoryInterface;
use App\Hexagonal\Count\Domain\Database\CountRepositoryPort;
use App\Entity\Count;

final class CountRepositoryPSQLAdapter implements CountRepositoryPort
{
    private EntityManagerInterface $entityManager;
    private ServiceEntityRepositoryInterface $repository;

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
        $this->repository = $this->entityManager->getRepository(Count::class);
    }

    /**
     * @inheritDoc
     */
    public function create(Count $count): Count
    {
        $this->entityManager->persist($count);
        $this->entityManager->flush();

        return $count;
    }

    /**
     * @inheritDoc
     */
    public function get(int $id): ?Count
    {
        return $this->repository->findOneBy(['id' => $id]);
    }

    /**
     * @inheritDoc
     */
    public function update(Count $count): Count
    {
        $this->entityManager->flush();

        return $count;
    }
}
