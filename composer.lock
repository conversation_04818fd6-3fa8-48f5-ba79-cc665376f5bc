{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "4d30b8702446882f0dc860b4df00d0b2", "packages": [{"name": "api-platform/api-pack", "version": "v1.3.0", "source": {"type": "git", "url": "https://github.com/api-platform/api-pack.git", "reference": "0fb12343362f565b65eb374d3c49bec580ffcf8d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/api-platform/api-pack/zipball/0fb12343362f565b65eb374d3c49bec580ffcf8d", "reference": "0fb12343362f565b65eb374d3c49bec580ffcf8d", "shasum": ""}, "require": {"api-platform/core": "*", "nelmio/cors-bundle": "*", "symfony/asset": "*", "symfony/expression-language": "*", "symfony/orm-pack": "*", "symfony/security-bundle": "*", "symfony/serializer-pack": "*", "symfony/twig-bundle": "*", "symfony/validator": "*"}, "type": "symfony-pack", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A pack for API Platform", "support": {"issues": "https://github.com/api-platform/api-pack/issues", "source": "https://github.com/api-platform/api-pack/tree/v1.3.0"}, "time": "2020-08-28T20:27:34+00:00"}, {"name": "api-platform/core", "version": "v2.6.3", "source": {"type": "git", "url": "https://github.com/api-platform/core.git", "reference": "1ba77218dfe61ca9e6cff1bbb3e9a4a6658d4538"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/api-platform/core/zipball/1ba77218dfe61ca9e6cff1bbb3e9a4a6658d4538", "reference": "1ba77218dfe61ca9e6cff1bbb3e9a4a6658d4538", "shasum": ""}, "require": {"doctrine/inflector": "^1.0 || ^2.0", "fig/link-util": "^1.0", "php": ">=7.1", "psr/cache": "^1.0", "psr/container": "^1.0", "symfony/http-foundation": "^4.4 || ^5.1", "symfony/http-kernel": "^4.4 || ^5.1", "symfony/property-access": "^3.4.19 || ^4.4 || ^5.1", "symfony/property-info": "^3.4 || ^4.4 || ^5.2.1", "symfony/serializer": "^4.4 || ^5.1", "symfony/web-link": "^4.4 || ^5.1", "willdurand/negotiation": "^2.0.3 || ^3.0"}, "conflict": {"doctrine/common": "<2.7", "doctrine/mongodb-odm": "<2.0", "doctrine/persistence": "<1.3"}, "require-dev": {"behat/behat": "^3.1", "behat/mink": "^1.7", "doctrine/annotations": "^1.7", "doctrine/common": "^2.11 || ^3.0", "doctrine/data-fixtures": "^1.2.2", "doctrine/doctrine-bundle": "^1.12 || ^2.0", "doctrine/mongodb-odm": "^2.0", "doctrine/mongodb-odm-bundle": "^4.0", "doctrine/orm": "^2.6.4 || ^3.0", "elasticsearch/elasticsearch": "^6.0 || ^7.0", "friends-of-behat/mink-browserkit-driver": "^1.3.1", "friends-of-behat/mink-extension": "^2.2", "friends-of-behat/symfony-extension": "^2.1", "guzzlehttp/guzzle": "^6.0 || ^7.0", "jangregor/phpstan-prophecy": "^0.8", "justinrainbow/json-schema": "^5.2.1", "phpdocumentor/reflection-docblock": "^3.0 || ^4.0 || ^5.1", "phpdocumentor/type-resolver": "^0.3 || ^0.4 || ^1.4", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12.65", "phpstan/phpstan-doctrine": "^0.12.7", "phpstan/phpstan-phpunit": "^0.12.4", "phpstan/phpstan-symfony": "^0.12.4", "psr/log": "^1.0", "ramsey/uuid": "^3.7 || ^4.0", "ramsey/uuid-doctrine": "^1.4", "soyuka/contexts": "^3.3.1", "soyuka/stubs-mongodb": "^1.0", "symfony/asset": "^3.4 || ^4.4 || ^5.1", "symfony/browser-kit": "^4.4 || ^5.1", "symfony/cache": "^3.4 || ^4.4 || ^5.1", "symfony/config": "^3.4 || ^4.4 || ^5.1", "symfony/console": "^3.4 || ^4.4 || ^5.1", "symfony/css-selector": "^3.4 || ^4.4 || ^5.1", "symfony/debug": "^3.4 || ^4.4 || ^5.1", "symfony/dependency-injection": "^3.4 || ^4.4 || ^5.1", "symfony/doctrine-bridge": "^3.4 || ^4.4 || ^5.1", "symfony/dom-crawler": "^3.4 || ^4.4 || ^5.1", "symfony/event-dispatcher": "^3.4 || ^4.4 || ^5.1", "symfony/expression-language": "^3.4 || ^4.4 || ^5.1", "symfony/finder": "^3.4 || ^4.4 || ^5.1", "symfony/form": "^3.4 || ^4.4 || ^5.1", "symfony/framework-bundle": "^4.4 || ^5.1", "symfony/http-client": "^4.4 || ^5.1", "symfony/mercure-bundle": "*", "symfony/messenger": "^4.4 || ^5.1", "symfony/phpunit-bridge": "^5.1.7", "symfony/routing": "^3.4 || ^4.4 || ^5.1", "symfony/security-bundle": "^3.4 || ^4.4 || ^5.1", "symfony/security-core": "^4.4 || ^5.1", "symfony/twig-bundle": "^3.4 || ^4.4 || ^5.1", "symfony/validator": "^3.4 || ^4.4 || ^5.1", "symfony/web-profiler-bundle": "^4.4 || ^5.1", "symfony/yaml": "^3.4 || ^4.4 || ^5.1", "twig/twig": "^1.42.3 || ^2.12 || ^3.0", "webonyx/graphql-php": "^14.0"}, "suggest": {"doctrine/mongodb-odm-bundle": "To support MongoDB. Only versions 4.0 and later are supported.", "elasticsearch/elasticsearch": "To support Elasticsearch.", "guzzlehttp/guzzle": "To use the HTTP cache invalidation system.", "ocramius/package-versions": "To display the API Platform's version in the debug bar.", "phpdocumentor/reflection-docblock": "To support extracting metadata from PHPDoc.", "psr/cache-implementation": "To use metadata caching.", "ramsey/uuid": "To support <PERSON>'s UUID identifiers.", "symfony/cache": "To have metadata caching when using Symfony integration.", "symfony/config": "To load XML configuration files.", "symfony/expression-language": "To use authorization features.", "symfony/security": "To use authorization features.", "symfony/twig-bundle": "To use the Swagger UI integration.", "symfony/uid": "To support Symfony UUID/ULID identifiers.", "symfony/web-profiler-bundle": "To use the data collector.", "webonyx/graphql-php": "To support GraphQL."}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.7.x-dev"}, "symfony": {"require": "^3.4 || ^4.4 || ^5.1"}}, "autoload": {"psr-4": {"ApiPlatform\\Core\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://dunglas.fr"}], "description": "Build a fully-featured hypermedia or GraphQL API in minutes!", "homepage": "https://api-platform.com", "keywords": ["Hydra", "JSON-LD", "api", "graphql", "hal", "jsonapi", "openapi", "rest", "swagger"], "support": {"issues": "https://github.com/api-platform/core/issues", "source": "https://github.com/api-platform/core/tree/v2.6.3"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/api-platform/core", "type": "tidelift"}], "time": "2021-03-07T19:13:33+00:00"}, {"name": "clue/socket-raw", "version": "v1.5.0", "source": {"type": "git", "url": "https://github.com/clue/php-socket-raw.git", "reference": "089ffa05fa75bdc4e919aac44bbc435b3ef640ef"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/clue/php-socket-raw/zipball/089ffa05fa75bdc4e919aac44bbc435b3ef640ef", "reference": "089ffa05fa75bdc4e919aac44bbc435b3ef640ef", "shasum": ""}, "require": {"ext-sockets": "*", "php": ">=5.3"}, "require-dev": {"phpunit/phpunit": "^9.3 || ^5.7 || ^4.8.35"}, "type": "library", "autoload": {"psr-4": {"Socket\\Raw\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Simple and lightweight OOP wrapper for PHP's low-level sockets extension (ext-sockets).", "homepage": "https://github.com/clue/php-socket-raw", "keywords": ["Socket", "client", "datagram", "dgram", "icmp", "ipv6", "server", "stream", "tcp", "udg", "udp", "unix"], "support": {"issues": "https://github.com/clue/php-socket-raw/issues", "source": "https://github.com/clue/php-socket-raw/tree/v1.5.0"}, "funding": [{"url": "https://clue.engineering/support", "type": "custom"}, {"url": "https://github.com/clue", "type": "github"}], "time": "2020-11-27T13:16:18+00:00"}, {"name": "codercat/jwk-to-pem", "version": "1.0", "source": {"type": "git", "url": "https://github.com/acodercat/php-jwk-to-pem.git", "reference": "e428b7abba5b37676e30e968930f718cf26724ac"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/acodercat/php-jwk-to-pem/zipball/e428b7abba5b37676e30e968930f718cf26724ac", "reference": "e428b7abba5b37676e30e968930f718cf26724ac", "shasum": ""}, "require": {"php": ">=7.0.0", "phpseclib/phpseclib": "~2.0"}, "require-dev": {"phpunit/phpunit": "^7.0"}, "type": "library", "autoload": {"psr-4": {"CoderCat\\JWKToPEM\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "codercat", "email": "<EMAIL>"}], "description": "Convert JWK to PEM format.", "support": {"issues": "https://github.com/acodercat/php-jwk-to-pem/issues", "source": "https://github.com/acodercat/php-jwk-to-pem/tree/1.0"}, "time": "2020-11-12T05:40:03+00:00"}, {"name": "composer/package-versions-deprecated", "version": "*********", "source": {"type": "git", "url": "https://github.com/composer/package-versions-deprecated.git", "reference": "68c9b502036e820c33445ff4d174327f6bb87486"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/package-versions-deprecated/zipball/68c9b502036e820c33445ff4d174327f6bb87486", "reference": "68c9b502036e820c33445ff4d174327f6bb87486", "shasum": ""}, "require": {"composer-plugin-api": "^1.1.0 || ^2.0", "php": "^7 || ^8"}, "replace": {"ocramius/package-versions": "1.10.99"}, "require-dev": {"composer/composer": "^1.9.3 || ^2.0@dev", "ext-zip": "^1.13", "phpunit/phpunit": "^6.5 || ^7"}, "type": "composer-plugin", "extra": {"class": "PackageVersions\\Installer", "branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"PackageVersions\\": "src/PackageVersions"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be"}], "description": "Composer plugin that provides efficient querying for installed package versions (no runtime IO)", "support": {"issues": "https://github.com/composer/package-versions-deprecated/issues", "source": "https://github.com/composer/package-versions-deprecated/tree/*********"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2020-08-13T12:55:41+00:00"}, {"name": "doctrine/annotations", "version": "1.12.1", "source": {"type": "git", "url": "https://github.com/doctrine/annotations.git", "reference": "b17c5014ef81d212ac539f07a1001832df1b6d3b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/annotations/zipball/b17c5014ef81d212ac539f07a1001832df1b6d3b", "reference": "b17c5014ef81d212ac539f07a1001832df1b6d3b", "shasum": ""}, "require": {"doctrine/lexer": "1.*", "ext-tokenizer": "*", "php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/cache": "1.*", "doctrine/coding-standard": "^6.0 || ^8.1", "phpstan/phpstan": "^0.12.20", "phpunit/phpunit": "^7.5 || ^9.1.5"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Annotations\\": "lib/Doctrine/Common/Annotations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Docblock Annotations Parser", "homepage": "https://www.doctrine-project.org/projects/annotations.html", "keywords": ["annotations", "doc<PERSON>", "parser"], "support": {"issues": "https://github.com/doctrine/annotations/issues", "source": "https://github.com/doctrine/annotations/tree/1.12.1"}, "time": "2021-02-21T21:00:45+00:00"}, {"name": "doctrine/cache", "version": "1.10.2", "source": {"type": "git", "url": "https://github.com/doctrine/cache.git", "reference": "13e3381b25847283a91948d04640543941309727"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/cache/zipball/13e3381b25847283a91948d04640543941309727", "reference": "13e3381b25847283a91948d04640543941309727", "shasum": ""}, "require": {"php": "~7.1 || ^8.0"}, "conflict": {"doctrine/common": ">2.2,<2.4"}, "require-dev": {"alcaeus/mongo-php-adapter": "^1.1", "doctrine/coding-standard": "^6.0", "mongodb/mongodb": "^1.1", "phpunit/phpunit": "^7.0", "predis/predis": "~1.0"}, "suggest": {"alcaeus/mongo-php-adapter": "Required to use legacy MongoDB driver"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.9.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\Cache\\": "lib/Doctrine/Common/Cache"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Cache library is a popular cache implementation that supports many different drivers such as redis, memcache, apc, mongodb and others.", "homepage": "https://www.doctrine-project.org/projects/cache.html", "keywords": ["abstraction", "apcu", "cache", "caching", "couchdb", "memcached", "php", "redis", "xcache"], "support": {"issues": "https://github.com/doctrine/cache/issues", "source": "https://github.com/doctrine/cache/tree/1.10.x"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fcache", "type": "tidelift"}], "time": "2020-07-07T18:54:01+00:00"}, {"name": "doctrine/collections", "version": "1.6.7", "source": {"type": "git", "url": "https://github.com/doctrine/collections.git", "reference": "55f8b799269a1a472457bd1a41b4f379d4cfba4a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/collections/zipball/55f8b799269a1a472457bd1a41b4f379d4cfba4a", "reference": "55f8b799269a1a472457bd1a41b4f379d4cfba4a", "shasum": ""}, "require": {"php": "^7.1.3 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^6.0", "phpstan/phpstan-shim": "^0.9.2", "phpunit/phpunit": "^7.0", "vimeo/psalm": "^3.8.1"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Collections\\": "lib/Doctrine/Common/Collections"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Collections library that adds additional functionality on top of PHP arrays.", "homepage": "https://www.doctrine-project.org/projects/collections.html", "keywords": ["array", "collections", "iterators", "php"], "support": {"issues": "https://github.com/doctrine/collections/issues", "source": "https://github.com/doctrine/collections/tree/1.6.7"}, "time": "2020-07-27T17:53:49+00:00"}, {"name": "doctrine/common", "version": "3.1.1", "source": {"type": "git", "url": "https://github.com/doctrine/common.git", "reference": "2afde5a9844126bc311cd5f548b5475e75f800d3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/common/zipball/2afde5a9844126bc311cd5f548b5475e75f800d3", "reference": "2afde5a9844126bc311cd5f548b5475e75f800d3", "shasum": ""}, "require": {"doctrine/persistence": "^2.0", "php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^6.0 || ^8.0", "phpstan/phpstan": "^0.12", "phpstan/phpstan-phpunit": "^0.12", "phpunit/phpunit": "^7.5.20 || ^8.5 || ^9.0", "squizlabs/php_codesniffer": "^3.0", "symfony/phpunit-bridge": "^4.0.5"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\": "lib/Doctrine/Common"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Doctrine Common project is a library that provides additional functionality that other Doctrine projects depend on such as better reflection support, proxies and much more.", "homepage": "https://www.doctrine-project.org/projects/common.html", "keywords": ["common", "doctrine", "php"], "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/3.1.1"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fcommon", "type": "tidelift"}], "time": "2021-01-20T19:58:05+00:00"}, {"name": "doctrine/dbal", "version": "2.12.1", "source": {"type": "git", "url": "https://github.com/doctrine/dbal.git", "reference": "adce7a954a1c2f14f85e94aed90c8489af204086"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/dbal/zipball/adce7a954a1c2f14f85e94aed90c8489af204086", "reference": "adce7a954a1c2f14f85e94aed90c8489af204086", "shasum": ""}, "require": {"doctrine/cache": "^1.0", "doctrine/event-manager": "^1.0", "ext-pdo": "*", "php": "^7.3 || ^8"}, "require-dev": {"doctrine/coding-standard": "^8.1", "jetbrains/phpstorm-stubs": "^2019.1", "phpstan/phpstan": "^0.12.40", "phpunit/phpunit": "^9.4", "psalm/plugin-phpunit": "^0.10.0", "symfony/console": "^2.0.5|^3.0|^4.0|^5.0", "vimeo/psalm": "^3.17.2"}, "suggest": {"symfony/console": "For helpful console commands such as SQL execution and import of files."}, "bin": ["bin/doctrine-dbal"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.0.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\DBAL\\": "lib/Doctrine/DBAL"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Powerful PHP database abstraction layer (DBAL) with many features for database schema introspection and management.", "homepage": "https://www.doctrine-project.org/projects/dbal.html", "keywords": ["abstraction", "database", "db2", "dbal", "ma<PERSON>b", "mssql", "mysql", "oci8", "oracle", "pdo", "pgsql", "postgresql", "queryobject", "sasql", "sql", "sqlanywhere", "sqlite", "sqlserver", "sqlsrv"], "support": {"issues": "https://github.com/doctrine/dbal/issues", "source": "https://github.com/doctrine/dbal/tree/2.12.1"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fdbal", "type": "tidelift"}], "time": "2020-11-14T20:26:58+00:00"}, {"name": "doctrine/doctrine-bundle", "version": "2.3.0", "source": {"type": "git", "url": "https://github.com/doctrine/DoctrineBundle.git", "reference": "8b922578bdee2243a26202b13df795e170efaef8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/DoctrineBundle/zipball/8b922578bdee2243a26202b13df795e170efaef8", "reference": "8b922578bdee2243a26202b13df795e170efaef8", "shasum": ""}, "require": {"doctrine/dbal": "^2.9.0|^3.0", "doctrine/persistence": "^1.3.3|^2.0", "doctrine/sql-formatter": "^1.0.1", "php": "^7.1 || ^8.0", "symfony/cache": "^4.3.3|^5.0", "symfony/config": "^4.3.3|^5.0", "symfony/console": "^3.4.30|^4.3.3|^5.0", "symfony/dependency-injection": "^4.3.3|^5.0", "symfony/doctrine-bridge": "^4.3.7|^5.0", "symfony/framework-bundle": "^3.4.30|^4.3.3|^5.0", "symfony/service-contracts": "^1.1.1|^2.0"}, "conflict": {"doctrine/orm": "<2.6", "twig/twig": "<1.34|>=2.0,<2.4"}, "require-dev": {"doctrine/coding-standard": "^8.0", "doctrine/orm": "^2.6", "friendsofphp/proxy-manager-lts": "^1.0", "phpunit/phpunit": "^7.5 || ^8.0 || ^9.3", "symfony/phpunit-bridge": "^4.2", "symfony/property-info": "^4.3.3|^5.0", "symfony/proxy-manager-bridge": "^3.4|^4.3.3|^5.0", "symfony/twig-bridge": "^3.4.30|^4.3.3|^5.0", "symfony/validator": "^3.4.30|^4.3.3|^5.0", "symfony/web-profiler-bundle": "^3.4.30|^4.3.3|^5.0", "symfony/yaml": "^3.4.30|^4.3.3|^5.0", "twig/twig": "^1.34|^2.12|^3.0"}, "suggest": {"doctrine/orm": "The Doctrine ORM integration is optional in the bundle.", "symfony/web-profiler-bundle": "To use the data collector."}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "2.3.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Bundle\\DoctrineBundle\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}, {"name": "Doctrine Project", "homepage": "http://www.doctrine-project.org/"}], "description": "Symfony DoctrineBundle", "homepage": "http://www.doctrine-project.org", "keywords": ["database", "dbal", "orm", "persistence"], "support": {"issues": "https://github.com/doctrine/DoctrineBundle/issues", "source": "https://github.com/doctrine/DoctrineBundle/tree/2.3.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fdoctrine-bundle", "type": "tidelift"}], "time": "2021-03-16T16:24:04+00:00"}, {"name": "doctrine/doctrine-migrations-bundle", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/doctrine/DoctrineMigrationsBundle.git", "reference": "b8de89fe811e62f1dea8cf9aafda0ea45ca6f1f3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/DoctrineMigrationsBundle/zipball/b8de89fe811e62f1dea8cf9aafda0ea45ca6f1f3", "reference": "b8de89fe811e62f1dea8cf9aafda0ea45ca6f1f3", "shasum": ""}, "require": {"doctrine/doctrine-bundle": "~1.0|~2.0", "doctrine/migrations": "~3.0", "php": "^7.2|^8.0", "symfony/framework-bundle": "~3.4|~4.0|~5.0"}, "require-dev": {"doctrine/coding-standard": "^8.0", "doctrine/orm": "^2.6", "phpstan/phpstan": "^0.12", "phpstan/phpstan-deprecation-rules": "^0.12", "phpstan/phpstan-phpunit": "^0.12", "phpstan/phpstan-strict-rules": "^0.12", "phpunit/phpunit": "^7.0|^8.0|^9.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Bundle\\MigrationsBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Doctrine Project", "homepage": "http://www.doctrine-project.org"}, {"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}], "description": "Symfony DoctrineMigrationsBundle", "homepage": "https://www.doctrine-project.org", "keywords": ["dbal", "migrations", "schema"], "support": {"issues": "https://github.com/doctrine/DoctrineMigrationsBundle/issues", "source": "https://github.com/doctrine/DoctrineMigrationsBundle/tree/3.0.2"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fdoctrine-migrations-bundle", "type": "tidelift"}], "time": "2020-12-23T15:13:22+00:00"}, {"name": "doctrine/event-manager", "version": "1.1.1", "source": {"type": "git", "url": "https://github.com/doctrine/event-manager.git", "reference": "41370af6a30faa9dc0368c4a6814d596e81aba7f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/event-manager/zipball/41370af6a30faa9dc0368c4a6814d596e81aba7f", "reference": "41370af6a30faa9dc0368c4a6814d596e81aba7f", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"doctrine/common": "<2.9@dev"}, "require-dev": {"doctrine/coding-standard": "^6.0", "phpunit/phpunit": "^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\": "lib/Doctrine/Common"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Event Manager is a simple PHP event system that was built to be used with the various Doctrine projects.", "homepage": "https://www.doctrine-project.org/projects/event-manager.html", "keywords": ["event", "event dispatcher", "event manager", "event system", "events"], "support": {"issues": "https://github.com/doctrine/event-manager/issues", "source": "https://github.com/doctrine/event-manager/tree/1.1.x"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fevent-manager", "type": "tidelift"}], "time": "2020-05-29T18:28:51+00:00"}, {"name": "doctrine/inflector", "version": "2.0.3", "source": {"type": "git", "url": "https://github.com/doctrine/inflector.git", "reference": "9cf661f4eb38f7c881cac67c75ea9b00bf97b210"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/inflector/zipball/9cf661f4eb38f7c881cac67c75ea9b00bf97b210", "reference": "9cf661f4eb38f7c881cac67c75ea9b00bf97b210", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^7.0", "phpstan/phpstan": "^0.11", "phpstan/phpstan-phpunit": "^0.11", "phpstan/phpstan-strict-rules": "^0.11", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Inflector\\": "lib/Doctrine/Inflector"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Inflector is a small library that can perform string manipulations with regard to upper/lowercase and singular/plural forms of words.", "homepage": "https://www.doctrine-project.org/projects/inflector.html", "keywords": ["inflection", "inflector", "lowercase", "manipulation", "php", "plural", "singular", "strings", "uppercase", "words"], "support": {"issues": "https://github.com/doctrine/inflector/issues", "source": "https://github.com/doctrine/inflector/tree/2.0.x"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finflector", "type": "tidelift"}], "time": "2020-05-29T15:13:26+00:00"}, {"name": "doctrine/instantiator", "version": "1.4.0", "source": {"type": "git", "url": "https://github.com/doctrine/instantiator.git", "reference": "d56bf6102915de5702778fe20f2de3b2fe570b5b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/instantiator/zipball/d56bf6102915de5702778fe20f2de3b2fe570b5b", "reference": "d56bf6102915de5702778fe20f2de3b2fe570b5b", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^8.0", "ext-pdo": "*", "ext-phar": "*", "phpbench/phpbench": "^0.13 || 1.0.0-alpha2", "phpstan/phpstan": "^0.12", "phpstan/phpstan-phpunit": "^0.12", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Instantiator\\": "src/Doctrine/Instantiator/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://ocramius.github.io/"}], "description": "A small, lightweight utility to instantiate objects in PHP without invoking their constructors", "homepage": "https://www.doctrine-project.org/projects/instantiator.html", "keywords": ["constructor", "instantiate"], "support": {"issues": "https://github.com/doctrine/instantiator/issues", "source": "https://github.com/doctrine/instantiator/tree/1.4.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finstantiator", "type": "tidelift"}], "time": "2020-11-10T18:47:58+00:00"}, {"name": "doctrine/lexer", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/doctrine/lexer.git", "reference": "e864bbf5904cb8f5bb334f99209b48018522f042"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/e864bbf5904cb8f5bb334f99209b48018522f042", "reference": "e864bbf5904cb8f5bb334f99209b48018522f042", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^6.0", "phpstan/phpstan": "^0.11.8", "phpunit/phpunit": "^8.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "lib/Doctrine/Common/Lexer"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "support": {"issues": "https://github.com/doctrine/lexer/issues", "source": "https://github.com/doctrine/lexer/tree/1.2.1"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Flexer", "type": "tidelift"}], "time": "2020-05-25T17:44:05+00:00"}, {"name": "doctrine/migrations", "version": "3.1.1", "source": {"type": "git", "url": "https://github.com/doctrine/migrations.git", "reference": "e543224170a61ffe49fcadb8e7339c345df1baa2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/migrations/zipball/e543224170a61ffe49fcadb8e7339c345df1baa2", "reference": "e543224170a61ffe49fcadb8e7339c345df1baa2", "shasum": ""}, "require": {"composer/package-versions-deprecated": "^1.8", "doctrine/dbal": "^2.10", "doctrine/event-manager": "^1.0", "friendsofphp/proxy-manager-lts": "^1.0", "php": "^7.2 || ^8.0", "psr/log": "^1.1.3", "symfony/console": "^3.4 || ^4.4.16 || ^5.0", "symfony/stopwatch": "^3.4 || ^4.0 || ^5.0"}, "require-dev": {"doctrine/coding-standard": "^8.0", "doctrine/orm": "^2.6", "doctrine/persistence": "^1.3 || ^2.0", "doctrine/sql-formatter": "^1.0", "ergebnis/composer-normalize": "^2.9", "ext-pdo_sqlite": "*", "phpstan/phpstan": "^0.12", "phpstan/phpstan-deprecation-rules": "^0.12", "phpstan/phpstan-phpunit": "^0.12", "phpstan/phpstan-strict-rules": "^0.12", "phpstan/phpstan-symfony": "^0.12", "phpunit/phpunit": "^8.5 || ^9.4", "symfony/process": "^3.4 || ^4.0 || ^5.0", "symfony/yaml": "^3.4 || ^4.0 || ^5.0"}, "suggest": {"doctrine/sql-formatter": "Allows to generate formatted SQL with the diff command.", "symfony/yaml": "Allows the use of yaml for migration configuration files."}, "bin": ["bin/doctrine-migrations"], "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}, "composer-normalize": {"indent-size": 4, "indent-style": "space"}}, "autoload": {"psr-4": {"Doctrine\\Migrations\\": "lib/Doctrine/Migrations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Doctrine Migrations project offer additional functionality on top of the database abstraction layer (DBAL) for versioning your database schema and easily deploying changes to it. It is a very easy to use and a powerful tool.", "homepage": "https://www.doctrine-project.org/projects/migrations.html", "keywords": ["database", "dbal", "migrations"], "support": {"issues": "https://github.com/doctrine/migrations/issues", "source": "https://github.com/doctrine/migrations/tree/3.1.1"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fmigrations", "type": "tidelift"}], "time": "2021-03-14T11:10:58+00:00"}, {"name": "doctrine/orm", "version": "2.8.2", "source": {"type": "git", "url": "https://github.com/doctrine/orm.git", "reference": "ebae57eb9637acd8252b398df3121b120688ed5c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/orm/zipball/ebae57eb9637acd8252b398df3121b120688ed5c", "reference": "ebae57eb9637acd8252b398df3121b120688ed5c", "shasum": ""}, "require": {"composer/package-versions-deprecated": "^1.8", "doctrine/annotations": "^1.11.1", "doctrine/cache": "^1.9.1", "doctrine/collections": "^1.5", "doctrine/common": "^3.0.3", "doctrine/dbal": "^2.10.0", "doctrine/event-manager": "^1.1", "doctrine/inflector": "^1.4|^2.0", "doctrine/instantiator": "^1.3", "doctrine/lexer": "^1.0", "doctrine/persistence": "^2.0", "ext-pdo": "*", "php": "^7.2|^8.0", "symfony/console": "^3.0|^4.0|^5.0"}, "require-dev": {"doctrine/coding-standard": "^8.0", "phpstan/phpstan": "^0.12.18", "phpunit/phpunit": "^8.5|^9.4", "symfony/yaml": "^3.4|^4.0|^5.0", "vimeo/psalm": "4.1.1"}, "suggest": {"symfony/yaml": "If you want to use YAML Metadata Mapping Driver"}, "bin": ["bin/doctrine"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.7.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\ORM\\": "lib/Doctrine/ORM"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Object-Relational-Mapper for PHP", "homepage": "https://www.doctrine-project.org/projects/orm.html", "keywords": ["database", "orm"], "support": {"issues": "https://github.com/doctrine/orm/issues", "source": "https://github.com/doctrine/orm/tree/2.8.2"}, "time": "2021-02-16T22:10:18+00:00"}, {"name": "doctrine/persistence", "version": "2.1.0", "source": {"type": "git", "url": "https://github.com/doctrine/persistence.git", "reference": "9899c16934053880876b920a3b8b02ed2337ac1d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/persistence/zipball/9899c16934053880876b920a3b8b02ed2337ac1d", "reference": "9899c16934053880876b920a3b8b02ed2337ac1d", "shasum": ""}, "require": {"doctrine/annotations": "^1.0", "doctrine/cache": "^1.0", "doctrine/collections": "^1.0", "doctrine/event-manager": "^1.0", "php": "^7.1 || ^8.0"}, "conflict": {"doctrine/common": "<2.10@dev"}, "require-dev": {"composer/package-versions-deprecated": "^1.11", "doctrine/coding-standard": "^6.0 || ^8.0", "doctrine/common": "^3.0", "phpstan/phpstan": "^0.12", "phpunit/phpunit": "^7.5.20 || ^8.0 || ^9.0", "vimeo/psalm": "^3.11"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\": "lib/Doctrine/Common", "Doctrine\\Persistence\\": "lib/Doctrine/Persistence"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Persistence project is a set of shared interfaces and functionality that the different Doctrine object mappers share.", "homepage": "https://doctrine-project.org/projects/persistence.html", "keywords": ["mapper", "object", "odm", "orm", "persistence"], "support": {"issues": "https://github.com/doctrine/persistence/issues", "source": "https://github.com/doctrine/persistence/tree/2.1.0"}, "time": "2020-10-24T22:13:54+00:00"}, {"name": "doctrine/sql-formatter", "version": "1.1.1", "source": {"type": "git", "url": "https://github.com/doctrine/sql-formatter.git", "reference": "56070bebac6e77230ed7d306ad13528e60732871"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/sql-formatter/zipball/56070bebac6e77230ed7d306ad13528e60732871", "reference": "56070bebac6e77230ed7d306ad13528e60732871", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.4"}, "bin": ["bin/sql-formatter"], "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\SqlFormatter\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://jeremydorn.com/"}], "description": "a PHP SQL highlighting library", "homepage": "https://github.com/doctrine/sql-formatter/", "keywords": ["highlight", "sql"], "support": {"issues": "https://github.com/doctrine/sql-formatter/issues", "source": "https://github.com/doctrine/sql-formatter/tree/1.1.x"}, "time": "2020-07-30T16:57:33+00:00"}, {"name": "fig/link-util", "version": "1.1.2", "source": {"type": "git", "url": "https://github.com/php-fig/link-util.git", "reference": "5d7b8d04ed3393b4b59968ca1e906fb7186d81e8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/link-util/zipball/5d7b8d04ed3393b4b59968ca1e906fb7186d81e8", "reference": "5d7b8d04ed3393b4b59968ca1e906fb7186d81e8", "shasum": ""}, "require": {"php": ">=5.5.0", "psr/link": "~1.0@dev"}, "provide": {"psr/link-implementation": "1.0"}, "require-dev": {"phpunit/phpunit": "^5.1", "squizlabs/php_codesniffer": "^2.3.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Fig\\Link\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common utility implementations for HTTP links", "keywords": ["http", "http-link", "link", "psr", "psr-13", "rest"], "support": {"issues": "https://github.com/php-fig/link-util/issues", "source": "https://github.com/php-fig/link-util/tree/1.1.2"}, "time": "2021-02-03T23:36:04+00:00"}, {"name": "firebase/php-jwt", "version": "v5.2.1", "source": {"type": "git", "url": "https://github.com/firebase/php-jwt.git", "reference": "f42c9110abe98dd6cfe9053c49bc86acc70b2d23"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/firebase/php-jwt/zipball/f42c9110abe98dd6cfe9053c49bc86acc70b2d23", "reference": "f42c9110abe98dd6cfe9053c49bc86acc70b2d23", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": ">=4.8 <=9"}, "type": "library", "autoload": {"psr-4": {"Firebase\\JWT\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A simple library to encode and decode JSON Web Tokens (JWT) in PHP. Should conform to the current spec.", "homepage": "https://github.com/firebase/php-jwt", "keywords": ["jwt", "php"], "support": {"issues": "https://github.com/firebase/php-jwt/issues", "source": "https://github.com/firebase/php-jwt/tree/v5.2.1"}, "time": "2021-02-12T00:02:00+00:00"}, {"name": "friendsofphp/proxy-manager-lts", "version": "v1.0.3", "source": {"type": "git", "url": "https://github.com/FriendsOfPHP/proxy-manager-lts.git", "reference": "121af47c9aee9c03031bdeca3fac0540f59aa5c3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/FriendsOfPHP/proxy-manager-lts/zipball/121af47c9aee9c03031bdeca3fac0540f59aa5c3", "reference": "121af47c9aee9c03031bdeca3fac0540f59aa5c3", "shasum": ""}, "require": {"laminas/laminas-code": "~3.4.1|^4.0", "php": ">=7.1", "symfony/filesystem": "^4.4.17|^5.0"}, "conflict": {"laminas/laminas-stdlib": "<3.2.1", "zendframework/zend-stdlib": "<3.2.1"}, "replace": {"ocramius/proxy-manager": "^2.1"}, "require-dev": {"ext-phar": "*", "symfony/phpunit-bridge": "^5.2"}, "type": "library", "extra": {"thanks": {"name": "ocramius/proxy-manager", "url": "https://github.com/Ocramius/ProxyManager"}}, "autoload": {"psr-4": {"ProxyManager\\": "src/ProxyManager"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ocramius.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Adding support for a wider range of PHP versions to ocramius/proxy-manager", "homepage": "https://github.com/FriendsOfPHP/proxy-manager-lts", "keywords": ["aop", "lazy loading", "proxy", "proxy pattern", "service proxies"], "support": {"issues": "https://github.com/FriendsOfPHP/proxy-manager-lts/issues", "source": "https://github.com/FriendsOfPHP/proxy-manager-lts/tree/v1.0.3"}, "funding": [{"url": "https://github.com/Ocramius", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/ocramius/proxy-manager", "type": "tidelift"}], "time": "2021-01-14T21:52:44+00:00"}, {"name": "google/auth", "version": "v1.15.0", "source": {"type": "git", "url": "https://github.com/googleapis/google-auth-library-php.git", "reference": "b346c07de6613e26443d7b4830e5e1933b830dc4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/googleapis/google-auth-library-php/zipball/b346c07de6613e26443d7b4830e5e1933b830dc4", "reference": "b346c07de6613e26443d7b4830e5e1933b830dc4", "shasum": ""}, "require": {"firebase/php-jwt": "~2.0|~3.0|~4.0|~5.0", "guzzlehttp/guzzle": "^5.3.1|^6.2.1|^7.0", "guzzlehttp/psr7": "^1.2", "php": ">=5.4", "psr/cache": "^1.0", "psr/http-message": "^1.0"}, "require-dev": {"guzzlehttp/promises": "0.1.1|^1.3", "kelvinmo/simplejwt": "^0.2.5|^0.5.1", "phpseclib/phpseclib": "^2", "phpunit/phpunit": "^4.8.36|^5.7", "sebastian/comparator": ">=1.2.3", "squizlabs/php_codesniffer": "^3.5"}, "suggest": {"phpseclib/phpseclib": "May be used in place of OpenSSL for signing strings or for token management. Please require version ^2."}, "type": "library", "autoload": {"psr-4": {"Google\\Auth\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "Google Auth Library for PHP", "homepage": "http://github.com/google/google-auth-library-php", "keywords": ["Authentication", "google", "oauth2"], "support": {"docs": "https://googleapis.github.io/google-auth-library-php/master/", "issues": "https://github.com/googleapis/google-auth-library-php/issues", "source": "https://github.com/googleapis/google-auth-library-php/tree/v1.15.0"}, "time": "2021-02-05T20:50:04+00:00"}, {"name": "googleads/googleads-php-lib", "version": "52.0.0", "source": {"type": "git", "url": "https://github.com/googleads/googleads-php-lib.git", "reference": "e6f22234b50c661ab9235bbd5360d711f5562768"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/googleads/googleads-php-lib/zipball/e6f22234b50c661ab9235bbd5360d711f5562768", "reference": "e6f22234b50c661ab9235bbd5360d711f5562768", "shasum": ""}, "require": {"ext-json": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-soap": "*", "google/auth": "^1.0.0", "guzzlehttp/guzzle": "^7.0", "guzzlehttp/psr7": "^1.2", "monolog/monolog": "^2.2.0", "php": ">=7.3", "phpdocumentor/reflection-docblock": "^3.0.3 || ^4.0 || ^5.0", "symfony/serializer": "^3.0.3 || ^4.4.0 || ^5.0.0"}, "require-dev": {"phpunit/phpunit": "^9.5.2", "squizlabs/php_codesniffer": "^2.9 || ^3.2"}, "type": "library", "autoload": {"psr-4": {"Google\\AdsApi\\": "src/Google/AdsApi/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Google", "homepage": "https://github.com/googleads/googleads-php-lib/contributors"}], "description": "Google Ads APIs Client Library for PHP (AdWords and Ad Manager)", "homepage": "https://github.com/googleads/googleads-php-lib", "support": {"issues": "https://github.com/googleads/googleads-php-lib/issues", "source": "https://github.com/googleads/googleads-php-lib/tree/52.0.0"}, "time": "2021-02-20T00:21:00+00:00"}, {"name": "guzzlehttp/guzzle", "version": "7.3.0", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "7008573787b430c1c1f650e3722d9bba59967628"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/7008573787b430c1c1f650e3722d9bba59967628", "reference": "7008573787b430c1c1f650e3722d9bba59967628", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.4", "guzzlehttp/psr7": "^1.7 || ^2.0", "php": "^7.2.5 || ^8.0", "psr/http-client": "^1.0"}, "provide": {"psr/http-client-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.4.1", "ext-curl": "*", "php-http/client-integration-tests": "^3.0", "phpunit/phpunit": "^8.5.5 || ^9.3.5", "psr/log": "^1.1"}, "suggest": {"ext-curl": "Required for CURL handler support", "ext-intl": "Required for Internationalized Domain Name (IDN) support", "psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"branch-alias": {"dev-master": "7.3-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "Guzzle is a PHP HTTP client library", "homepage": "http://guzzlephp.org/", "keywords": ["client", "curl", "framework", "http", "http client", "psr-18", "psr-7", "rest", "web service"], "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.3.0"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://github.com/ale<PERSON><PERSON><PERSON><PERSON>ov", "type": "github"}, {"url": "https://github.com/gmponos", "type": "github"}], "time": "2021-03-23T11:33:13+00:00"}, {"name": "guzzlehttp/promises", "version": "1.4.1", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "8e7d04f1f6450fef59366c399cfad4b9383aa30d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/8e7d04f1f6450fef59366c399cfad4b9383aa30d", "reference": "8e7d04f1f6450fef59366c399cfad4b9383aa30d", "shasum": ""}, "require": {"php": ">=5.5"}, "require-dev": {"symfony/phpunit-bridge": "^4.4 || ^5.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle promises library", "keywords": ["promise"], "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/1.4.1"}, "time": "2021-03-07T09:25:29+00:00"}, {"name": "guzzlehttp/psr7", "version": "1.8.1", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "35ea11d335fd638b5882ff1725228b3d35496ab1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/35ea11d335fd638b5882ff1725228b3d35496ab1", "reference": "35ea11d335fd638b5882ff1725228b3d35496ab1", "shasum": ""}, "require": {"php": ">=5.4.0", "psr/http-message": "~1.0", "ralouphie/getallheaders": "^2.0.5 || ^3.0.0"}, "provide": {"psr/http-message-implementation": "1.0"}, "require-dev": {"ext-zlib": "*", "phpunit/phpunit": "~4.8.36 || ^5.7.27 || ^6.5.14 || ^7.5.20 || ^8.5.8 || ^9.3.10"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.7-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "homepage": "https://github.com/Tobion"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "support": {"issues": "https://github.com/guzzle/psr7/issues", "source": "https://github.com/guzzle/psr7/tree/1.8.1"}, "time": "2021-03-21T16:25:00+00:00"}, {"name": "laminas/laminas-code", "version": "4.1.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-code.git", "reference": "5b553c274b94af3f880cbaaf8fbab047f279a31c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-code/zipball/5b553c274b94af3f880cbaaf8fbab047f279a31c", "reference": "5b553c274b94af3f880cbaaf8fbab047f279a31c", "shasum": ""}, "require": {"laminas/laminas-eventmanager": "^3.3", "php": "^7.4 || ~8.0.0"}, "conflict": {"phpspec/prophecy": "<1.9.0"}, "replace": {"zendframework/zend-code": "self.version"}, "require-dev": {"doctrine/annotations": "^1.10.4", "ext-phar": "*", "laminas/laminas-coding-standard": "^2.1.4", "laminas/laminas-stdlib": "^3.3.0", "phpunit/phpunit": "^9.4.2", "psalm/plugin-phpunit": "^0.14.0", "vimeo/psalm": "^4.3.1"}, "suggest": {"doctrine/annotations": "Doctrine\\Common\\Annotations >=1.0 for annotation features", "laminas/laminas-stdlib": "Laminas\\Stdlib component", "laminas/laminas-zendframework-bridge": "A bridge with Zend Framework"}, "type": "library", "autoload": {"psr-4": {"Laminas\\Code\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Extensions to the PHP Reflection API, static code scanning, and code generation", "homepage": "https://laminas.dev", "keywords": ["code", "laminas", "laminasframework"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-code/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-code/issues", "rss": "https://github.com/laminas/laminas-code/releases.atom", "source": "https://github.com/laminas/laminas-code"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2021-03-27T13:55:31+00:00"}, {"name": "laminas/laminas-eventmanager", "version": "3.3.1", "source": {"type": "git", "url": "https://github.com/laminas/laminas-eventmanager.git", "reference": "966c859b67867b179fde1eff0cd38df51472ce4a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-eventmanager/zipball/966c859b67867b179fde1eff0cd38df51472ce4a", "reference": "966c859b67867b179fde1eff0cd38df51472ce4a", "shasum": ""}, "require": {"laminas/laminas-zendframework-bridge": "^1.0", "php": "^7.3 || ^8.0"}, "replace": {"zendframework/zend-eventmanager": "^3.2.1"}, "require-dev": {"container-interop/container-interop": "^1.1", "laminas/laminas-coding-standard": "~1.0.0", "laminas/laminas-stdlib": "^2.7.3 || ^3.0", "phpbench/phpbench": "^0.17.1", "phpunit/phpunit": "^8.5.8"}, "suggest": {"container-interop/container-interop": "^1.1, to use the lazy listeners feature", "laminas/laminas-stdlib": "^2.7.3 || ^3.0, to use the FilterChain feature"}, "type": "library", "autoload": {"psr-4": {"Laminas\\EventManager\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Trigger and listen to events within a PHP application", "homepage": "https://laminas.dev", "keywords": ["event", "eventmanager", "events", "laminas"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-eventmanager/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-eventmanager/issues", "rss": "https://github.com/laminas/laminas-eventmanager/releases.atom", "source": "https://github.com/laminas/laminas-eventmanager"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2021-03-08T15:24:29+00:00"}, {"name": "laminas/laminas-zendframework-bridge", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-zendframework-bridge.git", "reference": "6cccbddfcfc742eb02158d6137ca5687d92cee32"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-zendframework-bridge/zipball/6cccbddfcfc742eb02158d6137ca5687d92cee32", "reference": "6cccbddfcfc742eb02158d6137ca5687d92cee32", "shasum": ""}, "require": {"php": "^7.3 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^5.7 || ^6.5 || ^7.5 || ^8.1 || ^9.3", "psalm/plugin-phpunit": "^0.15.1", "squizlabs/php_codesniffer": "^3.5", "vimeo/psalm": "^4.6"}, "type": "library", "extra": {"laminas": {"module": "Laminas\\ZendFrameworkBridge"}}, "autoload": {"files": ["src/autoload.php"], "psr-4": {"Laminas\\ZendFrameworkBridge\\": "src//"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Alias legacy ZF class names to Laminas Project equivalents.", "keywords": ["ZendFramework", "autoloading", "laminas", "zf"], "support": {"forum": "https://discourse.laminas.dev/", "issues": "https://github.com/laminas/laminas-zendframework-bridge/issues", "rss": "https://github.com/laminas/laminas-zendframework-bridge/releases.atom", "source": "https://github.com/laminas/laminas-zendframework-bridge"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2021-02-25T21:54:58+00:00"}, {"name": "monolog/monolog", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "1cb1cde8e8dd0f70cc0fe51354a59acad9302084"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/1cb1cde8e8dd0f70cc0fe51354a59acad9302084", "reference": "1cb1cde8e8dd0f70cc0fe51354a59acad9302084", "shasum": ""}, "require": {"php": ">=7.2", "psr/log": "^1.0.1"}, "provide": {"psr/log-implementation": "1.0.0"}, "require-dev": {"aws/aws-sdk-php": "^2.4.9 || ^3.0", "doctrine/couchdb": "~1.0@dev", "elasticsearch/elasticsearch": "^7", "graylog2/gelf-php": "^1.4.2", "mongodb/mongodb": "^1.8", "php-amqplib/php-amqplib": "~2.4", "php-console/php-console": "^3.1.3", "phpspec/prophecy": "^1.6.1", "phpstan/phpstan": "^0.12.59", "phpunit/phpunit": "^8.5", "predis/predis": "^1.1", "rollbar/rollbar": "^1.3", "ruflin/elastica": ">=0.90 <7.0.1", "swiftmailer/swiftmailer": "^5.3|^6.0"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "elasticsearch/elasticsearch": "Allow sending log messages to an Elasticsearch server via official client", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-mbstring": "Allow to work properly with unicode symbols", "ext-mongodb": "Allow sending log messages to a MongoDB server (via driver)", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server (via library)", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "php-console/php-console": "Allow sending log messages to Google Chrome", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.x-dev"}}, "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "https://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/2.2.0"}, "funding": [{"url": "https://github.com/Seldaek", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/monolog/monolog", "type": "tidelift"}], "time": "2020-12-14T13:15:25+00:00"}, {"name": "nelmio/cors-bundle", "version": "2.1.0", "source": {"type": "git", "url": "https://github.com/nelmio/NelmioCorsBundle.git", "reference": "be4d5824caebc86da9e224e935e02e1201b3ea54"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nelmio/NelmioCorsBundle/zipball/be4d5824caebc86da9e224e935e02e1201b3ea54", "reference": "be4d5824caebc86da9e224e935e02e1201b3ea54", "shasum": ""}, "require": {"symfony/framework-bundle": "^4.3 || ^5.0"}, "require-dev": {"mockery/mockery": "^1.2", "symfony/phpunit-bridge": "^4.3 || ^5.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Nelmio\\CorsBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Nelmio", "homepage": "http://nelm.io"}, {"name": "Symfony Community", "homepage": "https://github.com/nelmio/NelmioCorsBundle/contributors"}], "description": "Adds CORS (Cross-Origin Resource Sharing) headers support in your Symfony application", "keywords": ["api", "cors", "crossdomain"], "support": {"issues": "https://github.com/nelmio/NelmioCorsBundle/issues", "source": "https://github.com/nelmio/NelmioCorsBundle/tree/2.1.0"}, "time": "2020-07-22T11:44:28+00:00"}, {"name": "nesk/puphpeteer", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/rialto-php/puphpeteer.git", "reference": "0d51359c98cb6500edc033260be5944b23cbf573"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/rialto-php/puphpeteer/zipball/0d51359c98cb6500edc033260be5944b23cbf573", "reference": "0d51359c98cb6500edc033260be5944b23cbf573", "shasum": ""}, "require": {"nesk/rialto": "^1.2.0", "php": ">=7.3", "psr/log": "^1.0", "vierbergenlars/php-semver": "^3.0.2"}, "require-dev": {"monolog/monolog": "^2.0", "phpunit/phpunit": "^9.0", "symfony/process": "^4.0|^5.0"}, "type": "library", "autoload": {"psr-4": {"Nesk\\Puphpeteer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A Puppeteer bridge for PHP, supporting the entire API.", "keywords": ["automation", "developer-tools", "headless-chrome", "php", "puppeteer", "testing", "web"], "support": {"issues": "https://github.com/rialto-php/puphpeteer/issues", "source": "https://github.com/rialto-php/puphpeteer/tree/2.0.0"}, "funding": [{"url": "https://www.paypal.me/johann<PERSON><PERSON><PERSON>", "type": "custom"}, {"url": "https://github.com/nesk", "type": "github"}, {"url": "https://github.com/spekulatius", "type": "github"}], "time": "2020-12-01T11:49:36+00:00"}, {"name": "nesk/rialto", "version": "1.4.0", "source": {"type": "git", "url": "https://github.com/nesk/rialto.git", "reference": "a3db615d845cca42135fa1e271b323ffe904b83c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nesk/rialto/zipball/a3db615d845cca42135fa1e271b323ffe904b83c", "reference": "a3db615d845cca42135fa1e271b323ffe904b83c", "shasum": ""}, "require": {"clue/socket-raw": "^1.2", "php": ">=7.1", "psr/log": "^1.0", "symfony/process": "^3.3|^4.0|^5.0"}, "require-dev": {"codedungeon/phpunit-result-printer": ">=0.6 <1.0", "monolog/monolog": "^1.23", "phpunit/phpunit": "^6.5|^7.0"}, "suggest": {"ext-weakref": "Required to run all the tests"}, "type": "library", "autoload": {"psr-4": {"Nesk\\Rialto\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Manage Node resources from PHP", "keywords": ["Bridge", "Socket", "communication", "node", "php", "wrapper"], "support": {"issues": "https://github.com/nesk/rialto/issues", "source": "https://github.com/nesk/rialto/tree/1.4.0"}, "time": "2020-04-12T13:11:08+00:00"}, {"name": "phpdocumentor/reflection-common", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionCommon.git", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionCommon/zipball/1d01c49d4ed62f25aa84a747ad35d5a16924662b", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-2.x": "2.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common reflection classes used by phpdocumentor to reflect the code structure", "homepage": "http://www.phpdoc.org", "keywords": ["FQSEN", "phpDocumentor", "phpdoc", "reflection", "static analysis"], "support": {"issues": "https://github.com/phpDocumentor/ReflectionCommon/issues", "source": "https://github.com/phpDocumentor/ReflectionCommon/tree/2.x"}, "time": "2020-06-27T09:03:43+00:00"}, {"name": "phpdocumentor/reflection-docblock", "version": "5.2.2", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "reference": "069a785b2141f5bcf49f3e353548dc1cce6df556"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/069a785b2141f5bcf49f3e353548dc1cce6df556", "reference": "069a785b2141f5bcf49f3e353548dc1cce6df556", "shasum": ""}, "require": {"ext-filter": "*", "php": "^7.2 || ^8.0", "phpdocumentor/reflection-common": "^2.2", "phpdocumentor/type-resolver": "^1.3", "webmozart/assert": "^1.9.1"}, "require-dev": {"mockery/mockery": "~1.3.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "With this component, a library can provide support for annotations via DocBlocks or otherwise retrieve information that is embedded in a DocBlock.", "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/master"}, "time": "2020-09-03T19:13:55+00:00"}, {"name": "phpdocumentor/type-resolver", "version": "1.4.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/TypeResolver.git", "reference": "6a467b8989322d92aa1c8bf2bebcc6e5c2ba55c0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/6a467b8989322d92aa1c8bf2bebcc6e5c2ba55c0", "reference": "6a467b8989322d92aa1c8bf2bebcc6e5c2ba55c0", "shasum": ""}, "require": {"php": "^7.2 || ^8.0", "phpdocumentor/reflection-common": "^2.0"}, "require-dev": {"ext-tokenizer": "*"}, "type": "library", "extra": {"branch-alias": {"dev-1.x": "1.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A PSR-5 based resolver of Class names, Types and Structural Element Names", "support": {"issues": "https://github.com/phpDocumentor/TypeResolver/issues", "source": "https://github.com/phpDocumentor/TypeResolver/tree/1.4.0"}, "time": "2020-09-17T18:55:26+00:00"}, {"name": "phpseclib/phpseclib", "version": "2.0.30", "source": {"type": "git", "url": "https://github.com/phpseclib/phpseclib.git", "reference": "136b9ca7eebef78be14abf90d65c5e57b6bc5d36"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/136b9ca7eebef78be14abf90d65c5e57b6bc5d36", "reference": "136b9ca7eebef78be14abf90d65c5e57b6bc5d36", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"phing/phing": "~2.7", "phpunit/phpunit": "^4.8.35|^5.7|^6.0|^9.4", "squizlabs/php_codesniffer": "~2.0"}, "suggest": {"ext-gmp": "Install the GMP (GNU Multiple Precision) extension in order to speed up arbitrary precision integer arithmetic operations.", "ext-libsodium": "SSH2/SFTP can make use of some algorithms provided by the libsodium-php extension.", "ext-mcrypt": "Install the Mcrypt extension in order to speed up a few other cryptographic operations.", "ext-openssl": "Install the OpenSSL extension in order to speed up a wide variety of cryptographic operations."}, "type": "library", "autoload": {"files": ["phpseclib/bootstrap.php"], "psr-4": {"phpseclib\\": "phpseclib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Lead Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "PHP Secure Communications Library - Pure-PHP implementations of RSA, AES, SSH2, SFTP, X.509 etc.", "homepage": "http://phpseclib.sourceforge.net", "keywords": ["BigInteger", "aes", "asn.1", "asn1", "blowfish", "crypto", "cryptography", "encryption", "rsa", "security", "sftp", "signature", "signing", "ssh", "twofish", "x.509", "x509"], "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/2.0.30"}, "funding": [{"url": "https://github.com/terrafrost", "type": "github"}, {"url": "https://www.patreon.com/phpseclib", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/phpseclib/phpseclib", "type": "tidelift"}], "time": "2020-12-17T05:42:04+00:00"}, {"name": "psr/cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/d11b50ad223250cf17b86e38383413f5a6764bf8", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "support": {"source": "https://github.com/php-fig/cache/tree/master"}, "time": "2016-08-06T20:24:11+00:00"}, {"name": "psr/container", "version": "1.1.1", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "8622567409010282b7aeebe4bb841fe98b58dcaf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/8622567409010282b7aeebe4bb841fe98b58dcaf", "reference": "8622567409010282b7aeebe4bb841fe98b58dcaf", "shasum": ""}, "require": {"php": ">=7.2.0"}, "type": "library", "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/1.1.1"}, "time": "2021-03-05T17:36:06+00:00"}, {"name": "psr/event-dispatcher", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/event-dispatcher.git", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/event-dispatcher/zipball/dbefd12671e8a14ec7f180cab83036ed26714bb0", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0", "shasum": ""}, "require": {"php": ">=7.2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\EventDispatcher\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Standard interfaces for event handling.", "keywords": ["events", "psr", "psr-14"], "support": {"issues": "https://github.com/php-fig/event-dispatcher/issues", "source": "https://github.com/php-fig/event-dispatcher/tree/1.0.0"}, "time": "2019-01-08T18:20:26+00:00"}, {"name": "psr/http-client", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-client.git", "reference": "2dfb5f6c5eff0e91e20e913f8c5452ed95b86621"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/2dfb5f6c5eff0e91e20e913f8c5452ed95b86621", "reference": "2dfb5f6c5eff0e91e20e913f8c5452ed95b86621", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "support": {"source": "https://github.com/php-fig/http-client/tree/master"}, "time": "2020-06-29T06:28:15+00:00"}, {"name": "psr/http-message", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/f6561bf28d520154e4b0ec72be95418abe6d9363", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/master"}, "time": "2016-08-06T14:39:51+00:00"}, {"name": "psr/link", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/link.git", "reference": "eea8e8662d5cd3ae4517c9b864493f59fca95562"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/link/zipball/eea8e8662d5cd3ae4517c9b864493f59fca95562", "reference": "eea8e8662d5cd3ae4517c9b864493f59fca95562", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Link\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for HTTP links", "keywords": ["http", "http-link", "link", "psr", "psr-13", "rest"], "support": {"source": "https://github.com/php-fig/link/tree/master"}, "time": "2016-10-28T16:06:13+00:00"}, {"name": "psr/log", "version": "1.1.3", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "0f73288fd15629204f9d42b7055f72dacbe811fc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/0f73288fd15629204f9d42b7055f72dacbe811fc", "reference": "0f73288fd15629204f9d42b7055f72dacbe811fc", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/1.1.3"}, "time": "2020-03-23T09:12:05+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/develop"}, "time": "2019-03-08T08:55:37+00:00"}, {"name": "sensio/framework-extra-bundle", "version": "v5.6.1", "source": {"type": "git", "url": "https://github.com/sensiolabs/SensioFrameworkExtraBundle.git", "reference": "430d14c01836b77c28092883d195a43ce413ee32"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sensiolabs/SensioFrameworkExtraBundle/zipball/430d14c01836b77c28092883d195a43ce413ee32", "reference": "430d14c01836b77c28092883d195a43ce413ee32", "shasum": ""}, "require": {"doctrine/annotations": "^1.0", "php": ">=7.2.5", "symfony/config": "^4.4|^5.0", "symfony/dependency-injection": "^4.4|^5.0", "symfony/framework-bundle": "^4.4|^5.0", "symfony/http-kernel": "^4.4|^5.0"}, "conflict": {"doctrine/doctrine-cache-bundle": "<1.3.1", "doctrine/persistence": "<1.3"}, "require-dev": {"doctrine/dbal": "^2.10|^3.0", "doctrine/doctrine-bundle": "^1.11|^2.0", "doctrine/orm": "^2.5", "nyholm/psr7": "^1.1", "symfony/browser-kit": "^4.4|^5.0", "symfony/doctrine-bridge": "^4.4|^5.0", "symfony/dom-crawler": "^4.4|^5.0", "symfony/expression-language": "^4.4|^5.0", "symfony/finder": "^4.4|^5.0", "symfony/monolog-bridge": "^4.0|^5.0", "symfony/monolog-bundle": "^3.2", "symfony/phpunit-bridge": "^4.4.9|^5.0.9", "symfony/psr-http-message-bridge": "^1.1", "symfony/security-bundle": "^4.4|^5.0", "symfony/twig-bundle": "^4.4|^5.0", "symfony/yaml": "^4.4|^5.0", "twig/twig": "^1.34|^2.4|^3.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "5.6.x-dev"}}, "autoload": {"psr-4": {"Sensio\\Bundle\\FrameworkExtraBundle\\": "src/"}, "exclude-from-classmap": ["/tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "This bundle provides a way to configure your controllers with annotations", "keywords": ["annotations", "controllers"], "support": {"issues": "https://github.com/sensiolabs/SensioFrameworkExtraBundle/issues", "source": "https://github.com/sensiolabs/SensioFrameworkExtraBundle/tree/v5.6.1"}, "time": "2020-08-25T19:10:18+00:00"}, {"name": "symfony/asset", "version": "v5.2.4", "source": {"type": "git", "url": "https://github.com/symfony/asset.git", "reference": "54a42aa50f9359d1184bf7e954521b45ca3d5828"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/asset/zipball/54a42aa50f9359d1184bf7e954521b45ca3d5828", "reference": "54a42aa50f9359d1184bf7e954521b45ca3d5828", "shasum": ""}, "require": {"php": ">=7.2.5"}, "require-dev": {"symfony/http-client": "^4.4|^5.0", "symfony/http-foundation": "^4.4|^5.0", "symfony/http-kernel": "^4.4|^5.0"}, "suggest": {"symfony/http-foundation": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Asset\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Manages URL generation and versioning of web assets such as CSS stylesheets, JavaScript files and image files", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/asset/tree/v5.2.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-01-27T10:01:46+00:00"}, {"name": "symfony/cache", "version": "v5.2.6", "source": {"type": "git", "url": "https://github.com/symfony/cache.git", "reference": "093d69bb10c959553c8beb828b8d4ea250a247dd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/cache/zipball/093d69bb10c959553c8beb828b8d4ea250a247dd", "reference": "093d69bb10c959553c8beb828b8d4ea250a247dd", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/cache": "^1.0|^2.0", "psr/log": "^1.1", "symfony/cache-contracts": "^1.1.7|^2", "symfony/polyfill-php80": "^1.15", "symfony/service-contracts": "^1.1|^2", "symfony/var-exporter": "^4.4|^5.0"}, "conflict": {"doctrine/dbal": "<2.10", "symfony/dependency-injection": "<4.4", "symfony/http-kernel": "<4.4", "symfony/var-dumper": "<4.4"}, "provide": {"psr/cache-implementation": "1.0|2.0", "psr/simple-cache-implementation": "1.0", "symfony/cache-implementation": "1.0|2.0"}, "require-dev": {"cache/integration-tests": "dev-master", "doctrine/cache": "^1.6", "doctrine/dbal": "^2.10|^3.0", "predis/predis": "^1.1", "psr/simple-cache": "^1.0", "symfony/config": "^4.4|^5.0", "symfony/dependency-injection": "^4.4|^5.0", "symfony/filesystem": "^4.4|^5.0", "symfony/http-kernel": "^4.4|^5.0", "symfony/messenger": "^4.4|^5.0", "symfony/var-dumper": "^4.4|^5.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Cache\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an extended PSR-6, PSR-16 (and tags) implementation", "homepage": "https://symfony.com", "keywords": ["caching", "psr6"], "support": {"source": "https://github.com/symfony/cache/tree/v5.2.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-03-16T09:10:13+00:00"}, {"name": "symfony/cache-contracts", "version": "v2.2.0", "source": {"type": "git", "url": "https://github.com/symfony/cache-contracts.git", "reference": "8034ca0b61d4dd967f3698aaa1da2507b631d0cb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/cache-contracts/zipball/8034ca0b61d4dd967f3698aaa1da2507b631d0cb", "reference": "8034ca0b61d4dd967f3698aaa1da2507b631d0cb", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/cache": "^1.0"}, "suggest": {"symfony/cache-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Cache\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to caching", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/cache-contracts/tree/v2.2.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-09-07T11:33:47+00:00"}, {"name": "symfony/config", "version": "v5.2.4", "source": {"type": "git", "url": "https://github.com/symfony/config.git", "reference": "212d54675bf203ff8aef7d8cee8eecfb72f4a263"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/config/zipball/212d54675bf203ff8aef7d8cee8eecfb72f4a263", "reference": "212d54675bf203ff8aef7d8cee8eecfb72f4a263", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1", "symfony/filesystem": "^4.4|^5.0", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-php80": "^1.15"}, "conflict": {"symfony/finder": "<4.4"}, "require-dev": {"symfony/event-dispatcher": "^4.4|^5.0", "symfony/finder": "^4.4|^5.0", "symfony/messenger": "^4.4|^5.0", "symfony/service-contracts": "^1.1|^2", "symfony/yaml": "^4.4|^5.0"}, "suggest": {"symfony/yaml": "To use the yaml reference dumper"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Config\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Helps you find, load, combine, autofill and validate configuration values of any kind", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/config/tree/v5.2.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-02-23T23:58:19+00:00"}, {"name": "symfony/console", "version": "v5.2.6", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "35f039df40a3b335ebf310f244cb242b3a83ac8d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/35f039df40a3b335ebf310f244cb242b3a83ac8d", "reference": "35f039df40a3b335ebf310f244cb242b3a83ac8d", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php73": "^1.8", "symfony/polyfill-php80": "^1.15", "symfony/service-contracts": "^1.1|^2", "symfony/string": "^5.1"}, "conflict": {"symfony/dependency-injection": "<4.4", "symfony/dotenv": "<5.1", "symfony/event-dispatcher": "<4.4", "symfony/lock": "<4.4", "symfony/process": "<4.4"}, "provide": {"psr/log-implementation": "1.0"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "^4.4|^5.0", "symfony/dependency-injection": "^4.4|^5.0", "symfony/event-dispatcher": "^4.4|^5.0", "symfony/lock": "^4.4|^5.0", "symfony/process": "^4.4|^5.0", "symfony/var-dumper": "^4.4|^5.0"}, "suggest": {"psr/log": "For using the console logger", "symfony/event-dispatcher": "", "symfony/lock": "", "symfony/process": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases the creation of beautiful and testable command line interfaces", "homepage": "https://symfony.com", "keywords": ["cli", "command line", "console", "terminal"], "support": {"source": "https://github.com/symfony/console/tree/v5.2.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-03-28T09:42:18+00:00"}, {"name": "symfony/dependency-injection", "version": "v5.2.6", "source": {"type": "git", "url": "https://github.com/symfony/dependency-injection.git", "reference": "1e66194bed2a69fa395d26bf1067e5e34483afac"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dependency-injection/zipball/1e66194bed2a69fa395d26bf1067e5e34483afac", "reference": "1e66194bed2a69fa395d26bf1067e5e34483afac", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/container": "^1.0", "symfony/deprecation-contracts": "^2.1", "symfony/polyfill-php80": "^1.15", "symfony/service-contracts": "^1.1.6|^2"}, "conflict": {"symfony/config": "<5.1", "symfony/finder": "<4.4", "symfony/proxy-manager-bridge": "<4.4", "symfony/yaml": "<4.4"}, "provide": {"psr/container-implementation": "1.0", "symfony/service-implementation": "1.0|2.0"}, "require-dev": {"symfony/config": "^5.1", "symfony/expression-language": "^4.4|^5.0", "symfony/yaml": "^4.4|^5.0"}, "suggest": {"symfony/config": "", "symfony/expression-language": "For using expressions in service container configuration", "symfony/finder": "For using double-star glob patterns or when GLOB_BRACE portability is required", "symfony/proxy-manager-bridge": "Generate service proxies to lazy load them", "symfony/yaml": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\DependencyInjection\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows you to standardize and centralize the way objects are constructed in your application", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/dependency-injection/tree/v5.2.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-03-22T11:10:24+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v2.2.0", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "5fa56b4074d1ae755beb55617ddafe6f5d78f665"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/5fa56b4074d1ae755beb55617ddafe6f5d78f665", "reference": "5fa56b4074d1ae755beb55617ddafe6f5d78f665", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/master"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-09-07T11:33:47+00:00"}, {"name": "symfony/doctrine-bridge", "version": "v5.2.6", "source": {"type": "git", "url": "https://github.com/symfony/doctrine-bridge.git", "reference": "72b6d743c6108e2b8d15ab94e1a8a224c4d0d144"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/doctrine-bridge/zipball/72b6d743c6108e2b8d15ab94e1a8a224c4d0d144", "reference": "72b6d743c6108e2b8d15ab94e1a8a224c4d0d144", "shasum": ""}, "require": {"doctrine/event-manager": "~1.0", "doctrine/persistence": "^2", "php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "^1.15", "symfony/service-contracts": "^1.1|^2"}, "conflict": {"doctrine/dbal": "<2.10", "phpunit/phpunit": "<5.4.3", "symfony/dependency-injection": "<4.4", "symfony/form": "<5.1", "symfony/http-kernel": "<5", "symfony/messenger": "<4.4", "symfony/property-info": "<5", "symfony/security-bundle": "<5", "symfony/security-core": "<5", "symfony/validator": "<5.2"}, "require-dev": {"composer/package-versions-deprecated": "^1.8", "doctrine/annotations": "^1.10.4", "doctrine/cache": "~1.6", "doctrine/collections": "~1.0", "doctrine/data-fixtures": "^1.1", "doctrine/dbal": "^2.10|^3.0", "doctrine/orm": "^2.7.3", "symfony/cache": "^5.1", "symfony/config": "^4.4|^5.0", "symfony/dependency-injection": "^4.4|^5.0", "symfony/doctrine-messenger": "^5.1", "symfony/expression-language": "^4.4|^5.0", "symfony/form": "^5.1.3", "symfony/http-kernel": "^5.0", "symfony/messenger": "^4.4|^5.0", "symfony/property-access": "^4.4|^5.0", "symfony/property-info": "^5.0", "symfony/proxy-manager-bridge": "^4.4|^5.0", "symfony/security-core": "^5.0", "symfony/stopwatch": "^4.4|^5.0", "symfony/translation": "^4.4|^5.0", "symfony/uid": "^5.1", "symfony/validator": "^5.2", "symfony/var-dumper": "^4.4|^5.0"}, "suggest": {"doctrine/data-fixtures": "", "doctrine/dbal": "", "doctrine/orm": "", "symfony/form": "", "symfony/property-info": "", "symfony/validator": ""}, "type": "symfony-bridge", "autoload": {"psr-4": {"Symfony\\Bridge\\Doctrine\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides integration for Doctrine with various Symfony components", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/doctrine-bridge/tree/v5.2.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-03-10T22:10:15+00:00"}, {"name": "symfony/dotenv", "version": "v5.2.4", "source": {"type": "git", "url": "https://github.com/symfony/dotenv.git", "reference": "783f12027c6b40ab0e93d6136d9f642d1d67cd6b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dotenv/zipball/783f12027c6b40ab0e93d6136d9f642d1d67cd6b", "reference": "783f12027c6b40ab0e93d6136d9f642d1d67cd6b", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1"}, "require-dev": {"symfony/process": "^4.4|^5.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Dotenv\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Registers environment variables from a .env file", "homepage": "https://symfony.com", "keywords": ["dotenv", "env", "environment"], "support": {"source": "https://github.com/symfony/dotenv/tree/v5.2.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-01-27T10:01:46+00:00"}, {"name": "symfony/error-handler", "version": "v5.2.6", "source": {"type": "git", "url": "https://github.com/symfony/error-handler.git", "reference": "bdb7fb4188da7f4211e4b88350ba0dfdad002b03"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/error-handler/zipball/bdb7fb4188da7f4211e4b88350ba0dfdad002b03", "reference": "bdb7fb4188da7f4211e4b88350ba0dfdad002b03", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/log": "^1.0", "symfony/polyfill-php80": "^1.15", "symfony/var-dumper": "^4.4|^5.0"}, "require-dev": {"symfony/deprecation-contracts": "^2.1", "symfony/http-kernel": "^4.4|^5.0", "symfony/serializer": "^4.4|^5.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\ErrorHandler\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to manage errors and ease debugging PHP code", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/error-handler/tree/v5.2.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-03-16T09:07:47+00:00"}, {"name": "symfony/event-dispatcher", "version": "v5.2.4", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "d08d6ec121a425897951900ab692b612a61d6240"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/d08d6ec121a425897951900ab692b612a61d6240", "reference": "d08d6ec121a425897951900ab692b612a61d6240", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1", "symfony/event-dispatcher-contracts": "^2", "symfony/polyfill-php80": "^1.15"}, "conflict": {"symfony/dependency-injection": "<4.4"}, "provide": {"psr/event-dispatcher-implementation": "1.0", "symfony/event-dispatcher-implementation": "2.0"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "^4.4|^5.0", "symfony/dependency-injection": "^4.4|^5.0", "symfony/error-handler": "^4.4|^5.0", "symfony/expression-language": "^4.4|^5.0", "symfony/http-foundation": "^4.4|^5.0", "symfony/service-contracts": "^1.1|^2", "symfony/stopwatch": "^4.4|^5.0"}, "suggest": {"symfony/dependency-injection": "", "symfony/http-kernel": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools that allow your application components to communicate with each other by dispatching events and listening to them", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v5.2.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-02-18T17:12:37+00:00"}, {"name": "symfony/event-dispatcher-contracts", "version": "v2.2.0", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher-contracts.git", "reference": "0ba7d54483095a198fa51781bc608d17e84dffa2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/0ba7d54483095a198fa51781bc608d17e84dffa2", "reference": "0ba7d54483095a198fa51781bc608d17e84dffa2", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/event-dispatcher": "^1"}, "suggest": {"symfony/event-dispatcher-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\EventDispatcher\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to dispatching event", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v2.2.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-09-07T11:33:47+00:00"}, {"name": "symfony/expression-language", "version": "v5.2.4", "source": {"type": "git", "url": "https://github.com/symfony/expression-language.git", "reference": "3fc560e62bc5121751b792b11505db03a12cf83c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/expression-language/zipball/3fc560e62bc5121751b792b11505db03a12cf83c", "reference": "3fc560e62bc5121751b792b11505db03a12cf83c", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/cache": "^4.4|^5.0", "symfony/polyfill-php80": "^1.15", "symfony/service-contracts": "^1.1|^2"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\ExpressionLanguage\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an engine that can compile and evaluate expressions", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/expression-language/tree/v5.2.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-02-12T10:38:38+00:00"}, {"name": "symfony/filesystem", "version": "v5.2.6", "source": {"type": "git", "url": "https://github.com/symfony/filesystem.git", "reference": "8c86a82f51658188119e62cff0a050a12d09836f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/filesystem/zipball/8c86a82f51658188119e62cff0a050a12d09836f", "reference": "8c86a82f51658188119e62cff0a050a12d09836f", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-ctype": "~1.8"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Filesystem\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides basic utilities for the filesystem", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/filesystem/tree/v5.2.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-03-28T14:30:26+00:00"}, {"name": "symfony/finder", "version": "v5.2.4", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "0d639a0943822626290d169965804f79400e6a04"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/0d639a0943822626290d169965804f79400e6a04", "reference": "0d639a0943822626290d169965804f79400e6a04", "shasum": ""}, "require": {"php": ">=7.2.5"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Finds files and directories via an intuitive fluent interface", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/finder/tree/v5.2.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-02-15T18:55:04+00:00"}, {"name": "symfony/flex", "version": "v1.12.2", "source": {"type": "git", "url": "https://github.com/symfony/flex.git", "reference": "e472606b4b3173564f0edbca8f5d32b52fc4f2c9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/flex/zipball/e472606b4b3173564f0edbca8f5d32b52fc4f2c9", "reference": "e472606b4b3173564f0edbca8f5d32b52fc4f2c9", "shasum": ""}, "require": {"composer-plugin-api": "^1.0|^2.0", "php": ">=7.1"}, "require-dev": {"composer/composer": "^1.0.2|^2.0", "symfony/dotenv": "^4.4|^5.0", "symfony/filesystem": "^4.4|^5.0", "symfony/phpunit-bridge": "^4.4|^5.0", "symfony/process": "^3.4|^4.4|^5.0"}, "type": "composer-plugin", "extra": {"branch-alias": {"dev-main": "1.12-dev"}, "class": "Symfony\\Flex\\Flex"}, "autoload": {"psr-4": {"Symfony\\Flex\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Composer plugin for Symfony", "support": {"issues": "https://github.com/symfony/flex/issues", "source": "https://github.com/symfony/flex/tree/v1.12.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-02-16T14:05:05+00:00"}, {"name": "symfony/framework-bundle", "version": "v5.2.6", "source": {"type": "git", "url": "https://github.com/symfony/framework-bundle.git", "reference": "8889da18c6faa76c6149a90e6542be4afe723f2f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/framework-bundle/zipball/8889da18c6faa76c6149a90e6542be4afe723f2f", "reference": "8889da18c6faa76c6149a90e6542be4afe723f2f", "shasum": ""}, "require": {"ext-xml": "*", "php": ">=7.2.5", "symfony/cache": "^5.2", "symfony/config": "^5.0", "symfony/dependency-injection": "^5.2", "symfony/deprecation-contracts": "^2.1", "symfony/error-handler": "^4.4.1|^5.0.1", "symfony/event-dispatcher": "^5.1", "symfony/filesystem": "^4.4|^5.0", "symfony/finder": "^4.4|^5.0", "symfony/http-foundation": "^5.2.1", "symfony/http-kernel": "^5.2.1", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "^1.15", "symfony/routing": "^5.2"}, "conflict": {"doctrine/persistence": "<1.3", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "phpunit/phpunit": "<5.4.3", "symfony/asset": "<5.1", "symfony/browser-kit": "<4.4", "symfony/console": "<5.2.5", "symfony/dom-crawler": "<4.4", "symfony/dotenv": "<5.1", "symfony/form": "<5.2", "symfony/http-client": "<4.4", "symfony/lock": "<4.4", "symfony/mailer": "<5.2", "symfony/messenger": "<4.4", "symfony/mime": "<4.4", "symfony/property-access": "<5.2", "symfony/property-info": "<4.4", "symfony/serializer": "<5.2", "symfony/stopwatch": "<4.4", "symfony/translation": "<5.0", "symfony/twig-bridge": "<4.4", "symfony/twig-bundle": "<4.4", "symfony/validator": "<5.2", "symfony/web-profiler-bundle": "<4.4", "symfony/workflow": "<5.2"}, "require-dev": {"doctrine/annotations": "^1.10.4", "doctrine/cache": "~1.0", "doctrine/persistence": "^1.3|^2.0", "paragonie/sodium_compat": "^1.8", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/asset": "^5.1", "symfony/browser-kit": "^4.4|^5.0", "symfony/console": "^5.2", "symfony/css-selector": "^4.4|^5.0", "symfony/dom-crawler": "^4.4|^5.0", "symfony/dotenv": "^5.1", "symfony/expression-language": "^4.4|^5.0", "symfony/form": "^5.2", "symfony/http-client": "^4.4|^5.0", "symfony/lock": "^4.4|^5.0", "symfony/mailer": "^5.2", "symfony/messenger": "^5.2", "symfony/mime": "^4.4|^5.0", "symfony/polyfill-intl-icu": "~1.0", "symfony/process": "^4.4|^5.0", "symfony/property-info": "^4.4|^5.0", "symfony/security-bundle": "^5.1", "symfony/security-core": "^4.4|^5.2", "symfony/security-csrf": "^4.4|^5.0", "symfony/security-http": "^4.4|^5.0", "symfony/serializer": "^5.2", "symfony/stopwatch": "^4.4|^5.0", "symfony/string": "^5.0", "symfony/translation": "^5.0", "symfony/twig-bundle": "^4.4|^5.0", "symfony/validator": "^5.2", "symfony/web-link": "^4.4|^5.0", "symfony/workflow": "^5.2", "symfony/yaml": "^4.4|^5.0", "twig/twig": "^2.10|^3.0"}, "suggest": {"ext-apcu": "For best performance of the system caches", "symfony/console": "For using the console commands", "symfony/form": "For using forms", "symfony/property-info": "For using the property_info service", "symfony/serializer": "For using the serializer service", "symfony/validator": "For using validation", "symfony/web-link": "For using web links, features such as preloading, prefetching or prerendering", "symfony/yaml": "For using the debug:config and lint:yaml commands"}, "type": "symfony-bundle", "autoload": {"psr-4": {"Symfony\\Bundle\\FrameworkBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a tight integration between Symfony components and the Symfony full-stack framework", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/framework-bundle/tree/v5.2.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-03-22T14:43:01+00:00"}, {"name": "symfony/http-client", "version": "v5.2.6", "source": {"type": "git", "url": "https://github.com/symfony/http-client.git", "reference": "3c3075467da15bc2edf38d2ac20d34719e794bd8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-client/zipball/3c3075467da15bc2edf38d2ac20d34719e794bd8", "reference": "3c3075467da15bc2edf38d2ac20d34719e794bd8", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/log": "^1.0", "symfony/http-client-contracts": "^2.2", "symfony/polyfill-php73": "^1.11", "symfony/polyfill-php80": "^1.15", "symfony/service-contracts": "^1.0|^2"}, "provide": {"php-http/async-client-implementation": "*", "php-http/client-implementation": "*", "psr/http-client-implementation": "1.0", "symfony/http-client-implementation": "2.2"}, "require-dev": {"amphp/amp": "^2.5", "amphp/http-client": "^4.2.1", "amphp/http-tunnel": "^1.0", "amphp/socket": "^1.1", "guzzlehttp/promises": "^1.4", "nyholm/psr7": "^1.0", "php-http/httplug": "^1.0|^2.0", "psr/http-client": "^1.0", "symfony/dependency-injection": "^4.4|^5.0", "symfony/http-kernel": "^4.4.13|^5.1.5", "symfony/process": "^4.4|^5.0", "symfony/stopwatch": "^4.4|^5.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpClient\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides powerful methods to fetch HTTP resources synchronously or asynchronously", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-client/tree/v5.2.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-03-28T09:42:18+00:00"}, {"name": "symfony/http-client-contracts", "version": "v2.3.1", "source": {"type": "git", "url": "https://github.com/symfony/http-client-contracts.git", "reference": "41db680a15018f9c1d4b23516059633ce280ca33"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/41db680a15018f9c1d4b23516059633ce280ca33", "reference": "41db680a15018f9c1d4b23516059633ce280ca33", "shasum": ""}, "require": {"php": ">=7.2.5"}, "suggest": {"symfony/http-client-implementation": ""}, "type": "library", "extra": {"branch-version": "2.3", "branch-alias": {"dev-main": "2.3-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\HttpClient\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to HTTP clients", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/http-client-contracts/tree/v2.3.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-14T17:08:19+00:00"}, {"name": "symfony/http-foundation", "version": "v5.2.4", "source": {"type": "git", "url": "https://github.com/symfony/http-foundation.git", "reference": "54499baea7f7418bce7b5ec92770fd0799e8e9bf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-foundation/zipball/54499baea7f7418bce7b5ec92770fd0799e8e9bf", "reference": "54499baea7f7418bce7b5ec92770fd0799e8e9bf", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1", "symfony/polyfill-mbstring": "~1.1", "symfony/polyfill-php80": "^1.15"}, "require-dev": {"predis/predis": "~1.0", "symfony/cache": "^4.4|^5.0", "symfony/expression-language": "^4.4|^5.0", "symfony/mime": "^4.4|^5.0"}, "suggest": {"symfony/mime": "To use the file extension guesser"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpFoundation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Defines an object-oriented layer for the HTTP specification", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-foundation/tree/v5.2.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-02-25T17:16:57+00:00"}, {"name": "symfony/http-kernel", "version": "v5.2.6", "source": {"type": "git", "url": "https://github.com/symfony/http-kernel.git", "reference": "f34de4c61ca46df73857f7f36b9a3805bdd7e3b2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-kernel/zipball/f34de4c61ca46df73857f7f36b9a3805bdd7e3b2", "reference": "f34de4c61ca46df73857f7f36b9a3805bdd7e3b2", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/log": "~1.0", "symfony/deprecation-contracts": "^2.1", "symfony/error-handler": "^4.4|^5.0", "symfony/event-dispatcher": "^5.0", "symfony/http-client-contracts": "^1.1|^2", "symfony/http-foundation": "^4.4|^5.0", "symfony/polyfill-ctype": "^1.8", "symfony/polyfill-php73": "^1.9", "symfony/polyfill-php80": "^1.15"}, "conflict": {"symfony/browser-kit": "<4.4", "symfony/cache": "<5.0", "symfony/config": "<5.0", "symfony/console": "<4.4", "symfony/dependency-injection": "<5.1.8", "symfony/doctrine-bridge": "<5.0", "symfony/form": "<5.0", "symfony/http-client": "<5.0", "symfony/mailer": "<5.0", "symfony/messenger": "<5.0", "symfony/translation": "<5.0", "symfony/twig-bridge": "<5.0", "symfony/validator": "<5.0", "twig/twig": "<2.13"}, "provide": {"psr/log-implementation": "1.0"}, "require-dev": {"psr/cache": "^1.0|^2.0|^3.0", "symfony/browser-kit": "^4.4|^5.0", "symfony/config": "^5.0", "symfony/console": "^4.4|^5.0", "symfony/css-selector": "^4.4|^5.0", "symfony/dependency-injection": "^5.1.8", "symfony/dom-crawler": "^4.4|^5.0", "symfony/expression-language": "^4.4|^5.0", "symfony/finder": "^4.4|^5.0", "symfony/process": "^4.4|^5.0", "symfony/routing": "^4.4|^5.0", "symfony/stopwatch": "^4.4|^5.0", "symfony/translation": "^4.4|^5.0", "symfony/translation-contracts": "^1.1|^2", "twig/twig": "^2.13|^3.0.4"}, "suggest": {"symfony/browser-kit": "", "symfony/config": "", "symfony/console": "", "symfony/dependency-injection": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpKernel\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a structured process for converting a Request into a Response", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-kernel/tree/v5.2.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-03-29T05:16:58+00:00"}, {"name": "symfony/intl", "version": "v5.2.4", "source": {"type": "git", "url": "https://github.com/symfony/intl.git", "reference": "11b4217e394c80a2e313d3a4a37262fbe65a7add"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/intl/zipball/11b4217e394c80a2e313d3a4a37262fbe65a7add", "reference": "11b4217e394c80a2e313d3a4a37262fbe65a7add", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-intl-icu": "~1.0", "symfony/polyfill-php80": "^1.15"}, "require-dev": {"symfony/filesystem": "^4.4|^5.0"}, "suggest": {"ext-intl": "to use the component with locales other than \"en\""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Intl\\": ""}, "classmap": ["Resources/stubs"], "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "i<PERSON>@wiedler.ch"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a PHP replacement layer for the C intl extension that includes additional data from the ICU library", "homepage": "https://symfony.com", "keywords": ["i18n", "icu", "internationalization", "intl", "l10n", "localization"], "support": {"source": "https://github.com/symfony/intl/tree/v5.2.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-02-18T22:42:36+00:00"}, {"name": "symfony/mime", "version": "v5.2.6", "source": {"type": "git", "url": "https://github.com/symfony/mime.git", "reference": "1b2092244374cbe48ae733673f2ca0818b37197b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mime/zipball/1b2092244374cbe48ae733673f2ca0818b37197b", "reference": "1b2092244374cbe48ae733673f2ca0818b37197b", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0", "symfony/polyfill-php80": "^1.15"}, "conflict": {"egulias/email-validator": "~3.0.0", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/mailer": "<4.4"}, "require-dev": {"egulias/email-validator": "^2.1.10|^3.1", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/dependency-injection": "^4.4|^5.0", "symfony/property-access": "^4.4|^5.1", "symfony/property-info": "^4.4|^5.1", "symfony/serializer": "^5.2"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Mime\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows manipulating MIME messages", "homepage": "https://symfony.com", "keywords": ["mime", "mime-type"], "support": {"source": "https://github.com/symfony/mime/tree/v5.2.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-03-12T13:18:39+00:00"}, {"name": "symfony/monolog-bridge", "version": "v5.2.5", "source": {"type": "git", "url": "https://github.com/symfony/monolog-bridge.git", "reference": "8a330ab86c4bdf3983b26abf64bf85574edf0d52"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/monolog-bridge/zipball/8a330ab86c4bdf3983b26abf64bf85574edf0d52", "reference": "8a330ab86c4bdf3983b26abf64bf85574edf0d52", "shasum": ""}, "require": {"monolog/monolog": "^1.25.1|^2", "php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1", "symfony/http-kernel": "^4.4|^5.0", "symfony/service-contracts": "^1.1|^2"}, "conflict": {"symfony/console": "<4.4", "symfony/http-foundation": "<4.4"}, "require-dev": {"symfony/console": "^4.4|^5.0", "symfony/http-client": "^4.4|^5.0", "symfony/mailer": "^4.4|^5.0", "symfony/mime": "^4.4|^5.0", "symfony/security-core": "^4.4|^5.0", "symfony/var-dumper": "^4.4|^5.0"}, "suggest": {"symfony/console": "For the possibility to show log messages in console commands depending on verbosity settings.", "symfony/http-kernel": "For using the debugging handlers together with the response life cycle of the HTTP kernel.", "symfony/var-dumper": "For using the debugging handlers like the console handler or the log server handler."}, "type": "symfony-bridge", "autoload": {"psr-4": {"Symfony\\Bridge\\Monolog\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides integration for Monolog with various Symfony components", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/monolog-bridge/tree/v5.2.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-03-06T07:59:01+00:00"}, {"name": "symfony/monolog-bundle", "version": "v3.6.0", "source": {"type": "git", "url": "https://github.com/symfony/monolog-bundle.git", "reference": "e495f5c7e4e672ffef4357d4a4d85f010802f940"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/monolog-bundle/zipball/e495f5c7e4e672ffef4357d4a4d85f010802f940", "reference": "e495f5c7e4e672ffef4357d4a4d85f010802f940", "shasum": ""}, "require": {"monolog/monolog": "~1.22 || ~2.0", "php": ">=5.6", "symfony/config": "~3.4 || ~4.0 || ^5.0", "symfony/dependency-injection": "~3.4.10 || ^4.0.10 || ^5.0", "symfony/http-kernel": "~3.4 || ~4.0 || ^5.0", "symfony/monolog-bridge": "~3.4 || ~4.0 || ^5.0"}, "require-dev": {"symfony/console": "~3.4 || ~4.0 || ^5.0", "symfony/phpunit-bridge": "^4.4 || ^5.0", "symfony/yaml": "~3.4 || ~4.0 || ^5.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\MonologBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}], "description": "Symfony MonologBundle", "homepage": "http://symfony.com", "keywords": ["log", "logging"], "support": {"issues": "https://github.com/symfony/monolog-bundle/issues", "source": "https://github.com/symfony/monolog-bundle/tree/v3.6.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-06T15:12:11+00:00"}, {"name": "symfony/orm-pack", "version": "v2.1.0", "source": {"type": "git", "url": "https://github.com/symfony/orm-pack.git", "reference": "357f6362067b1ebb94af321b79f8939fc9118751"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/orm-pack/zipball/357f6362067b1ebb94af321b79f8939fc9118751", "reference": "357f6362067b1ebb94af321b79f8939fc9118751", "shasum": ""}, "require": {"composer/package-versions-deprecated": "*", "doctrine/doctrine-bundle": "*", "doctrine/doctrine-migrations-bundle": "*", "doctrine/orm": "*", "symfony/proxy-manager-bridge": "*"}, "type": "symfony-pack", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A pack for the Doctrine ORM", "support": {"issues": "https://github.com/symfony/orm-pack/issues", "source": "https://github.com/symfony/orm-pack/tree/v2.1.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-12-22T16:33:52+00:00"}, {"name": "symfony/polyfill-intl-grapheme", "version": "v1.22.1", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-grapheme.git", "reference": "5601e09b69f26c1828b13b6bb87cb07cddba3170"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/5601e09b69f26c1828b13b6bb87cb07cddba3170", "reference": "5601e09b69f26c1828b13b6bb87cb07cddba3170", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.22-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Intl\\Grapheme\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's grapheme_* functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "grapheme", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.22.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-01-22T09:19:47+00:00"}, {"name": "symfony/polyfill-intl-icu", "version": "v1.22.1", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-icu.git", "reference": "af1842919c7e7364aaaa2798b29839e3ba168588"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-icu/zipball/af1842919c7e7364aaaa2798b29839e3ba168588", "reference": "af1842919c7e7364aaaa2798b29839e3ba168588", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-intl": "For best performance and support of other locales than \"en\""}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.22-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Icu\\": ""}, "classmap": ["Resources/stubs"], "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's ICU-related data and classes", "homepage": "https://symfony.com", "keywords": ["compatibility", "icu", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-icu/tree/v1.22.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-01-22T09:19:47+00:00"}, {"name": "symfony/polyfill-intl-idn", "version": "v1.22.1", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-idn.git", "reference": "2d63434d922daf7da8dd863e7907e67ee3031483"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/2d63434d922daf7da8dd863e7907e67ee3031483", "reference": "2d63434d922daf7da8dd863e7907e67ee3031483", "shasum": ""}, "require": {"php": ">=7.1", "symfony/polyfill-intl-normalizer": "^1.10", "symfony/polyfill-php72": "^1.10"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.22-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Intl\\Idn\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "idn", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/v1.22.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-01-22T09:19:47+00:00"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.22.1", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "43a0283138253ed1d48d352ab6d0bdb3f809f248"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/43a0283138253ed1d48d352ab6d0bdb3f809f248", "reference": "43a0283138253ed1d48d352ab6d0bdb3f809f248", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.22-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "files": ["bootstrap.php"], "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.22.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-01-22T09:19:47+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.22.1", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "5232de97ee3b75b0360528dae24e73db49566ab1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/5232de97ee3b75b0360528dae24e73db49566ab1", "reference": "5232de97ee3b75b0360528dae24e73db49566ab1", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.22-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.22.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-01-22T09:19:47+00:00"}, {"name": "symfony/polyfill-php73", "version": "v1.22.1", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php73.git", "reference": "a678b42e92f86eca04b7fa4c0f6f19d097fb69e2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php73/zipball/a678b42e92f86eca04b7fa4c0f6f19d097fb69e2", "reference": "a678b42e92f86eca04b7fa4c0f6f19d097fb69e2", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.22-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Php73\\": ""}, "files": ["bootstrap.php"], "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php73/tree/v1.22.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-01-07T16:49:33+00:00"}, {"name": "symfony/polyfill-php80", "version": "v1.22.1", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php80.git", "reference": "dc3063ba22c2a1fd2f45ed856374d79114998f91"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/dc3063ba22c2a1fd2f45ed856374d79114998f91", "reference": "dc3063ba22c2a1fd2f45ed856374d79114998f91", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.22-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "files": ["bootstrap.php"], "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.22.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-01-07T16:49:33+00:00"}, {"name": "symfony/process", "version": "v5.2.4", "source": {"type": "git", "url": "https://github.com/symfony/process.git", "reference": "313a38f09c77fbcdc1d223e57d368cea76a2fd2f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/process/zipball/313a38f09c77fbcdc1d223e57d368cea76a2fd2f", "reference": "313a38f09c77fbcdc1d223e57d368cea76a2fd2f", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-php80": "^1.15"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Executes commands in sub-processes", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/process/tree/v5.2.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-01-27T10:15:41+00:00"}, {"name": "symfony/property-access", "version": "v5.2.4", "source": {"type": "git", "url": "https://github.com/symfony/property-access.git", "reference": "3af8ed262bd3217512a13b023981fe68f36ad5f3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/property-access/zipball/3af8ed262bd3217512a13b023981fe68f36ad5f3", "reference": "3af8ed262bd3217512a13b023981fe68f36ad5f3", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1", "symfony/polyfill-php80": "^1.15", "symfony/property-info": "^5.2"}, "require-dev": {"symfony/cache": "^4.4|^5.0"}, "suggest": {"psr/cache-implementation": "To cache access methods."}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\PropertyAccess\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides functions to read and write from/to an object or array using a simple string notation", "homepage": "https://symfony.com", "keywords": ["access", "array", "extraction", "index", "injection", "object", "property", "property path", "reflection"], "support": {"source": "https://github.com/symfony/property-access/tree/v5.2.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-01-27T10:15:41+00:00"}, {"name": "symfony/property-info", "version": "v5.2.4", "source": {"type": "git", "url": "https://github.com/symfony/property-info.git", "reference": "7185bbc74e6f49c3f1b5936b4d9e4ca133921189"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/property-info/zipball/7185bbc74e6f49c3f1b5936b4d9e4ca133921189", "reference": "7185bbc74e6f49c3f1b5936b4d9e4ca133921189", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1", "symfony/polyfill-php80": "^1.15", "symfony/string": "^5.1"}, "conflict": {"phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/dependency-injection": "<4.4"}, "require-dev": {"doctrine/annotations": "^1.10.4", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/cache": "^4.4|^5.0", "symfony/dependency-injection": "^4.4|^5.0", "symfony/serializer": "^4.4|^5.0"}, "suggest": {"phpdocumentor/reflection-docblock": "To use the PHPDoc", "psr/cache-implementation": "To cache results", "symfony/doctrine-bridge": "To use Doctrine metadata", "symfony/serializer": "To use Serializer metadata"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\PropertyInfo\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Extracts information about PHP class' properties using metadata of popular sources", "homepage": "https://symfony.com", "keywords": ["doctrine", "phpdoc", "property", "symfony", "type", "validator"], "support": {"source": "https://github.com/symfony/property-info/tree/v5.2.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-02-17T15:24:54+00:00"}, {"name": "symfony/proxy-manager-bridge", "version": "v5.2.4", "source": {"type": "git", "url": "https://github.com/symfony/proxy-manager-bridge.git", "reference": "fd6bb40190b1719abbe831be09adf38e0744d5f5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/proxy-manager-bridge/zipball/fd6bb40190b1719abbe831be09adf38e0744d5f5", "reference": "fd6bb40190b1719abbe831be09adf38e0744d5f5", "shasum": ""}, "require": {"composer/package-versions-deprecated": "^1.8", "friendsofphp/proxy-manager-lts": "^1.0.2", "php": ">=7.2.5", "symfony/dependency-injection": "^5.0"}, "require-dev": {"symfony/config": "^4.4|^5.0"}, "type": "symfony-bridge", "autoload": {"psr-4": {"Symfony\\Bridge\\ProxyManager\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides integration for ProxyManager with various Symfony components", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/proxy-manager-bridge/tree/v5.2.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-01-27T10:01:46+00:00"}, {"name": "symfony/routing", "version": "v5.2.6", "source": {"type": "git", "url": "https://github.com/symfony/routing.git", "reference": "31fba555f178afd04d54fd26953501b2c3f0c6e6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/routing/zipball/31fba555f178afd04d54fd26953501b2c3f0c6e6", "reference": "31fba555f178afd04d54fd26953501b2c3f0c6e6", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1", "symfony/polyfill-php80": "^1.15"}, "conflict": {"symfony/config": "<5.0", "symfony/dependency-injection": "<4.4", "symfony/yaml": "<4.4"}, "require-dev": {"doctrine/annotations": "^1.10.4", "psr/log": "~1.0", "symfony/config": "^5.0", "symfony/dependency-injection": "^4.4|^5.0", "symfony/expression-language": "^4.4|^5.0", "symfony/http-foundation": "^4.4|^5.0", "symfony/yaml": "^4.4|^5.0"}, "suggest": {"doctrine/annotations": "For using the annotation loader", "symfony/config": "For using the all-in-one router or any loader", "symfony/expression-language": "For using expression matching", "symfony/http-foundation": "For using a Symfony Request object", "symfony/yaml": "For using the YAML loader"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Routing\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Maps an HTTP request to a set of configuration variables", "homepage": "https://symfony.com", "keywords": ["router", "routing", "uri", "url"], "support": {"source": "https://github.com/symfony/routing/tree/v5.2.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-03-14T13:53:33+00:00"}, {"name": "symfony/security-bundle", "version": "v5.2.6", "source": {"type": "git", "url": "https://github.com/symfony/security-bundle.git", "reference": "26550782d6e6db6fbfd7702ae0ccef7964c3535d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/security-bundle/zipball/26550782d6e6db6fbfd7702ae0ccef7964c3535d", "reference": "26550782d6e6db6fbfd7702ae0ccef7964c3535d", "shasum": ""}, "require": {"ext-xml": "*", "php": ">=7.2.5", "symfony/config": "^4.4|^5.0", "symfony/dependency-injection": "^5.2", "symfony/deprecation-contracts": "^2.1", "symfony/event-dispatcher": "^5.1", "symfony/http-kernel": "^5.0", "symfony/polyfill-php80": "^1.15", "symfony/security-core": "^5.2", "symfony/security-csrf": "^4.4|^5.0", "symfony/security-guard": "^5.2", "symfony/security-http": "^5.2"}, "conflict": {"symfony/browser-kit": "<4.4", "symfony/console": "<4.4", "symfony/framework-bundle": "<4.4", "symfony/ldap": "<4.4", "symfony/twig-bundle": "<4.4"}, "require-dev": {"doctrine/doctrine-bundle": "^2.0", "symfony/asset": "^4.4|^5.0", "symfony/browser-kit": "^4.4|^5.0", "symfony/console": "^4.4|^5.0", "symfony/css-selector": "^4.4|^5.0", "symfony/dom-crawler": "^4.4|^5.0", "symfony/expression-language": "^4.4|^5.0", "symfony/form": "^4.4|^5.0", "symfony/framework-bundle": "^5.2", "symfony/process": "^4.4|^5.0", "symfony/rate-limiter": "^5.2", "symfony/serializer": "^4.4|^5.0", "symfony/translation": "^4.4|^5.0", "symfony/twig-bridge": "^4.4|^5.0", "symfony/twig-bundle": "^4.4|^5.0", "symfony/validator": "^4.4|^5.0", "symfony/yaml": "^4.4|^5.0", "twig/twig": "^2.13|^3.0.4"}, "type": "symfony-bundle", "autoload": {"psr-4": {"Symfony\\Bundle\\SecurityBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a tight integration of the Security component into the Symfony full-stack framework", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/security-bundle/tree/v5.2.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-03-23T12:45:44+00:00"}, {"name": "symfony/security-core", "version": "v5.2.6", "source": {"type": "git", "url": "https://github.com/symfony/security-core.git", "reference": "9dcedab1c2c637fc9a377b3a9313a61087609760"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/security-core/zipball/9dcedab1c2c637fc9a377b3a9313a61087609760", "reference": "9dcedab1c2c637fc9a377b3a9313a61087609760", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1", "symfony/event-dispatcher-contracts": "^1.1|^2", "symfony/polyfill-php80": "^1.15", "symfony/service-contracts": "^1.1.6|^2"}, "conflict": {"symfony/event-dispatcher": "<4.4", "symfony/ldap": "<4.4", "symfony/security-guard": "<4.4", "symfony/validator": "<5.2"}, "require-dev": {"psr/container": "^1.0|^2.0", "psr/log": "~1.0", "symfony/event-dispatcher": "^4.4|^5.0", "symfony/expression-language": "^4.4|^5.0", "symfony/http-foundation": "^4.4|^5.0", "symfony/ldap": "^4.4|^5.0", "symfony/translation": "^4.4|^5.0", "symfony/validator": "^5.2"}, "suggest": {"psr/container-implementation": "To instantiate the Security class", "symfony/event-dispatcher": "", "symfony/expression-language": "For using the expression voter", "symfony/http-foundation": "", "symfony/ldap": "For using LDAP integration", "symfony/validator": "For using the user password constraint"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Security\\Core\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Security Component - Core Library", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/security-core/tree/v5.2.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-03-10T22:10:15+00:00"}, {"name": "symfony/security-csrf", "version": "v5.2.4", "source": {"type": "git", "url": "https://github.com/symfony/security-csrf.git", "reference": "e22ef49d5d3773014942f3dfe301b168a4a833dc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/security-csrf/zipball/e22ef49d5d3773014942f3dfe301b168a4a833dc", "reference": "e22ef49d5d3773014942f3dfe301b168a4a833dc", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/security-core": "^4.4|^5.0"}, "conflict": {"symfony/http-foundation": "<4.4"}, "require-dev": {"symfony/http-foundation": "^4.4|^5.0"}, "suggest": {"symfony/http-foundation": "For using the class SessionTokenStorage."}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Security\\Csrf\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Security Component - CSRF Library", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/security-csrf/tree/v5.2.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-01-27T10:01:46+00:00"}, {"name": "symfony/security-guard", "version": "v5.2.4", "source": {"type": "git", "url": "https://github.com/symfony/security-guard.git", "reference": "a191352047f2ea0d927c62e1a2f261cf906d1bde"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/security-guard/zipball/a191352047f2ea0d927c62e1a2f261cf906d1bde", "reference": "a191352047f2ea0d927c62e1a2f261cf906d1bde", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-php80": "^1.15", "symfony/security-core": "^5.0", "symfony/security-http": "^4.4.1|^5.0.1"}, "require-dev": {"psr/log": "~1.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Security\\Guard\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Security Component - Guard", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/security-guard/tree/v5.2.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-01-27T10:15:41+00:00"}, {"name": "symfony/security-http", "version": "v5.2.6", "source": {"type": "git", "url": "https://github.com/symfony/security-http.git", "reference": "a4b3d122ee0c263850ca1ed620faa1b3a8e7584b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/security-http/zipball/a4b3d122ee0c263850ca1ed620faa1b3a8e7584b", "reference": "a4b3d122ee0c263850ca1ed620faa1b3a8e7584b", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1", "symfony/http-foundation": "^5.2", "symfony/http-kernel": "^5.2", "symfony/polyfill-php80": "^1.15", "symfony/property-access": "^4.4|^5.0", "symfony/security-core": "^5.2"}, "conflict": {"symfony/event-dispatcher": "<4.3", "symfony/security-csrf": "<4.4"}, "require-dev": {"psr/log": "~1.0", "symfony/cache": "^4.4|^5.0", "symfony/rate-limiter": "^5.2", "symfony/routing": "^4.4|^5.0", "symfony/security-csrf": "^4.4|^5.0", "symfony/translation": "^4.4|^5.0"}, "suggest": {"symfony/routing": "For using the HttpUtils class to create sub-requests, redirect the user, and match URLs", "symfony/security-csrf": "For using tokens to protect authentication/logout attempts"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Security\\Http\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Security Component - HTTP Integration", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/security-http/tree/v5.2.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-03-28T17:23:00+00:00"}, {"name": "symfony/serializer", "version": "v5.2.4", "source": {"type": "git", "url": "https://github.com/symfony/serializer.git", "reference": "a285f474a72397ccbd384900abc968ffcb511dda"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/serializer/zipball/a285f474a72397ccbd384900abc968ffcb511dda", "reference": "a285f474a72397ccbd384900abc968ffcb511dda", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-php80": "^1.15"}, "conflict": {"phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/dependency-injection": "<4.4", "symfony/property-access": "<4.4", "symfony/property-info": "<4.4", "symfony/yaml": "<4.4"}, "require-dev": {"doctrine/annotations": "^1.10.4", "doctrine/cache": "~1.0", "phpdocumentor/reflection-docblock": "^3.2|^4.0|^5.0", "symfony/cache": "^4.4|^5.0", "symfony/config": "^4.4|^5.0", "symfony/dependency-injection": "^4.4|^5.0", "symfony/error-handler": "^4.4|^5.0", "symfony/filesystem": "^4.4|^5.0", "symfony/form": "^4.4|^5.0", "symfony/http-foundation": "^4.4|^5.0", "symfony/http-kernel": "^4.4|^5.0", "symfony/mime": "^4.4|^5.0", "symfony/property-access": "^4.4.9|^5.0.9", "symfony/property-info": "^4.4|^5.0", "symfony/uid": "^5.1", "symfony/validator": "^4.4|^5.0", "symfony/var-exporter": "^4.4|^5.0", "symfony/yaml": "^4.4|^5.0"}, "suggest": {"doctrine/annotations": "For using the annotation mapping. You will also need doctrine/cache.", "doctrine/cache": "For using the default cached annotation reader and metadata cache.", "psr/cache-implementation": "For using the metadata cache.", "symfony/config": "For using the XML mapping loader.", "symfony/mime": "For using a MIME type guesser within the DataUriNormalizer.", "symfony/property-access": "For using the ObjectNormalizer.", "symfony/property-info": "To deserialize relations.", "symfony/var-exporter": "For using the metadata compiler.", "symfony/yaml": "For using the default YAML mapping loader."}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Serializer\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Handles serializing and deserializing data structures, including object graphs, into array structures or other formats like XML and JSON.", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/serializer/tree/v5.2.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-03-02T12:14:02+00:00"}, {"name": "symfony/serializer-pack", "version": "v1.0.4", "source": {"type": "git", "url": "https://github.com/symfony/serializer-pack.git", "reference": "61173947057d5e1bf1c79e2a6ab6a8430be0602e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/serializer-pack/zipball/61173947057d5e1bf1c79e2a6ab6a8430be0602e", "reference": "61173947057d5e1bf1c79e2a6ab6a8430be0602e", "shasum": ""}, "require": {"doctrine/annotations": "^1.0", "phpdocumentor/reflection-docblock": "*", "symfony/property-access": "*", "symfony/property-info": "*", "symfony/serializer": "*"}, "type": "symfony-pack", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A pack for the Symfony serializer", "support": {"issues": "https://github.com/symfony/serializer-pack/issues", "source": "https://github.com/symfony/serializer-pack/tree/v1.0.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-19T08:52:16+00:00"}, {"name": "symfony/service-contracts", "version": "v2.2.0", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "d15da7ba4957ffb8f1747218be9e1a121fd298a1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/d15da7ba4957ffb8f1747218be9e1a121fd298a1", "reference": "d15da7ba4957ffb8f1747218be9e1a121fd298a1", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/container": "^1.0"}, "suggest": {"symfony/service-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/service-contracts/tree/master"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-09-07T11:33:47+00:00"}, {"name": "symfony/stopwatch", "version": "v5.2.4", "source": {"type": "git", "url": "https://github.com/symfony/stopwatch.git", "reference": "b12274acfab9d9850c52583d136a24398cdf1a0c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/stopwatch/zipball/b12274acfab9d9850c52583d136a24398cdf1a0c", "reference": "b12274acfab9d9850c52583d136a24398cdf1a0c", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/service-contracts": "^1.0|^2"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Stopwatch\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a way to profile code", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/stopwatch/tree/v5.2.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-01-27T10:15:41+00:00"}, {"name": "symfony/string", "version": "v5.2.6", "source": {"type": "git", "url": "https://github.com/symfony/string.git", "reference": "ad0bd91bce2054103f5eaa18ebeba8d3bc2a0572"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/string/zipball/ad0bd91bce2054103f5eaa18ebeba8d3bc2a0572", "reference": "ad0bd91bce2054103f5eaa18ebeba8d3bc2a0572", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-intl-grapheme": "~1.0", "symfony/polyfill-intl-normalizer": "~1.0", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "~1.15"}, "require-dev": {"symfony/error-handler": "^4.4|^5.0", "symfony/http-client": "^4.4|^5.0", "symfony/translation-contracts": "^1.1|^2", "symfony/var-exporter": "^4.4|^5.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\String\\": ""}, "files": ["Resources/functions.php"], "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an object-oriented API to strings and deals with bytes, UTF-8 code points and grapheme clusters in a unified way", "homepage": "https://symfony.com", "keywords": ["grapheme", "i18n", "string", "unicode", "utf-8", "utf8"], "support": {"source": "https://github.com/symfony/string/tree/v5.2.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-03-17T17:12:15+00:00"}, {"name": "symfony/translation-contracts", "version": "v2.3.0", "source": {"type": "git", "url": "https://github.com/symfony/translation-contracts.git", "reference": "e2eaa60b558f26a4b0354e1bbb25636efaaad105"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation-contracts/zipball/e2eaa60b558f26a4b0354e1bbb25636efaaad105", "reference": "e2eaa60b558f26a4b0354e1bbb25636efaaad105", "shasum": ""}, "require": {"php": ">=7.2.5"}, "suggest": {"symfony/translation-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.3-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Translation\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to translation", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/translation-contracts/tree/v2.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-09-28T13:05:58+00:00"}, {"name": "symfony/twig-bridge", "version": "v5.2.6", "source": {"type": "git", "url": "https://github.com/symfony/twig-bridge.git", "reference": "a65d8d38c66f147f29b73d53d14e8c9a983653b8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/twig-bridge/zipball/a65d8d38c66f147f29b73d53d14e8c9a983653b8", "reference": "a65d8d38c66f147f29b73d53d14e8c9a983653b8", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-php80": "^1.15", "symfony/translation-contracts": "^1.1|^2", "twig/twig": "^2.13|^3.0.4"}, "conflict": {"phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/console": "<4.4", "symfony/form": "<5.1", "symfony/http-foundation": "<4.4", "symfony/http-kernel": "<4.4", "symfony/translation": "<5.2", "symfony/workflow": "<5.2"}, "require-dev": {"egulias/email-validator": "^2.1.10|^3", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/asset": "^4.4|^5.0", "symfony/console": "^4.4|^5.0", "symfony/dependency-injection": "^4.4|^5.0", "symfony/expression-language": "^4.4|^5.0", "symfony/finder": "^4.4|^5.0", "symfony/form": "^5.1.9", "symfony/http-foundation": "^4.4|^5.0", "symfony/http-kernel": "^4.4|^5.0", "symfony/intl": "^4.4|^5.0", "symfony/mime": "^5.2", "symfony/polyfill-intl-icu": "~1.0", "symfony/property-info": "^4.4|^5.1", "symfony/routing": "^4.4|^5.0", "symfony/security-acl": "^2.8|^3.0", "symfony/security-core": "^4.4|^5.0", "symfony/security-csrf": "^4.4|^5.0", "symfony/security-http": "^4.4|^5.0", "symfony/serializer": "^5.2", "symfony/stopwatch": "^4.4|^5.0", "symfony/translation": "^5.2", "symfony/web-link": "^4.4|^5.0", "symfony/workflow": "^5.2", "symfony/yaml": "^4.4|^5.0", "twig/cssinliner-extra": "^2.12|^3", "twig/inky-extra": "^2.12|^3", "twig/markdown-extra": "^2.12|^3"}, "suggest": {"symfony/asset": "For using the AssetExtension", "symfony/expression-language": "For using the ExpressionExtension", "symfony/finder": "", "symfony/form": "For using the FormExtension", "symfony/http-kernel": "For using the HttpKernelExtension", "symfony/routing": "For using the RoutingExtension", "symfony/security-core": "For using the SecurityExtension", "symfony/security-csrf": "For using the CsrfExtension", "symfony/security-http": "For using the LogoutUrlExtension", "symfony/stopwatch": "For using the StopwatchExtension", "symfony/translation": "For using the TranslationExtension", "symfony/var-dumper": "For using the DumpExtension", "symfony/web-link": "For using the WebLinkExtension", "symfony/yaml": "For using the YamlExtension"}, "type": "symfony-bridge", "autoload": {"psr-4": {"Symfony\\Bridge\\Twig\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides integration for Twig with various Symfony components", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/twig-bridge/tree/v5.2.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-03-16T09:10:13+00:00"}, {"name": "symfony/twig-bundle", "version": "v5.2.4", "source": {"type": "git", "url": "https://github.com/symfony/twig-bundle.git", "reference": "5ebbb5f0e8bfaa0b4b37cb25ff97f83b18caf221"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/twig-bundle/zipball/5ebbb5f0e8bfaa0b4b37cb25ff97f83b18caf221", "reference": "5ebbb5f0e8bfaa0b4b37cb25ff97f83b18caf221", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/config": "^4.4|^5.0", "symfony/http-foundation": "^4.4|^5.0", "symfony/http-kernel": "^5.0", "symfony/polyfill-ctype": "~1.8", "symfony/twig-bridge": "^5.0", "twig/twig": "^2.13|^3.0.4"}, "conflict": {"symfony/dependency-injection": "<5.2", "symfony/framework-bundle": "<5.0", "symfony/translation": "<5.0"}, "require-dev": {"doctrine/annotations": "^1.10.4", "doctrine/cache": "~1.0", "symfony/asset": "^4.4|^5.0", "symfony/dependency-injection": "^5.2", "symfony/expression-language": "^4.4|^5.0", "symfony/finder": "^4.4|^5.0", "symfony/form": "^4.4|^5.0", "symfony/framework-bundle": "^5.0", "symfony/routing": "^4.4|^5.0", "symfony/stopwatch": "^4.4|^5.0", "symfony/translation": "^5.0", "symfony/web-link": "^4.4|^5.0", "symfony/yaml": "^4.4|^5.0"}, "type": "symfony-bundle", "autoload": {"psr-4": {"Symfony\\Bundle\\TwigBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a tight integration of Twig into the Symfony full-stack framework", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/twig-bundle/tree/v5.2.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-01-27T10:15:41+00:00"}, {"name": "symfony/validator", "version": "v5.2.6", "source": {"type": "git", "url": "https://github.com/symfony/validator.git", "reference": "67354644f9baba3cc122134c255b7461b7e0fe31"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/validator/zipball/67354644f9baba3cc122134c255b7461b7e0fe31", "reference": "67354644f9baba3cc122134c255b7461b7e0fe31", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php73": "~1.0", "symfony/polyfill-php80": "^1.15", "symfony/translation-contracts": "^1.1|^2"}, "conflict": {"doctrine/lexer": "<1.0.2", "phpunit/phpunit": "<5.4.3", "symfony/dependency-injection": "<4.4", "symfony/expression-language": "<5.1", "symfony/http-kernel": "<4.4", "symfony/intl": "<4.4", "symfony/translation": "<4.4", "symfony/yaml": "<4.4"}, "require-dev": {"doctrine/annotations": "^1.10.4", "doctrine/cache": "~1.0", "egulias/email-validator": "^2.1.10|^3", "symfony/cache": "^4.4|^5.0", "symfony/config": "^4.4|^5.0", "symfony/console": "^4.4|^5.0", "symfony/dependency-injection": "^4.4|^5.0", "symfony/expression-language": "^5.1", "symfony/finder": "^4.4|^5.0", "symfony/http-client": "^4.4|^5.0", "symfony/http-foundation": "^4.4|^5.0", "symfony/http-kernel": "^4.4|^5.0", "symfony/intl": "^4.4|^5.0", "symfony/mime": "^4.4|^5.0", "symfony/property-access": "^4.4|^5.0", "symfony/property-info": "^4.4|^5.0", "symfony/translation": "^4.4|^5.0", "symfony/yaml": "^4.4|^5.0"}, "suggest": {"doctrine/annotations": "For using the annotation mapping. You will also need doctrine/cache.", "doctrine/cache": "For using the default cached annotation reader.", "egulias/email-validator": "Strict (RFC compliant) email validation", "psr/cache-implementation": "For using the mapping cache.", "symfony/config": "", "symfony/expression-language": "For using the Expression validator and the ExpressionLanguageSyntax constraints", "symfony/http-foundation": "", "symfony/intl": "", "symfony/property-access": "For accessing properties within comparison constraints", "symfony/property-info": "To automatically add NotNull and Type constraints", "symfony/translation": "For translating validation errors.", "symfony/yaml": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Validator\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to validate values", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/validator/tree/v5.2.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-03-23T12:45:44+00:00"}, {"name": "symfony/var-dumper", "version": "v5.2.6", "source": {"type": "git", "url": "https://github.com/symfony/var-dumper.git", "reference": "89412a68ea2e675b4e44f260a5666729f77f668e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-dumper/zipball/89412a68ea2e675b4e44f260a5666729f77f668e", "reference": "89412a68ea2e675b4e44f260a5666729f77f668e", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "^1.15"}, "conflict": {"phpunit/phpunit": "<5.4.3", "symfony/console": "<4.4"}, "require-dev": {"ext-iconv": "*", "symfony/console": "^4.4|^5.0", "symfony/process": "^4.4|^5.0", "twig/twig": "^2.13|^3.0.4"}, "suggest": {"ext-iconv": "To convert non-UTF-8 strings to UTF-8 (or symfony/polyfill-iconv in case ext-iconv cannot be used).", "ext-intl": "To show region name in time zone dump", "symfony/console": "To use the ServerDumpCommand and/or the bin/var-dump-server script"}, "bin": ["Resources/bin/var-dump-server"], "type": "library", "autoload": {"files": ["Resources/functions/dump.php"], "psr-4": {"Symfony\\Component\\VarDumper\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides mechanisms for walking through any arbitrary PHP variable", "homepage": "https://symfony.com", "keywords": ["debug", "dump"], "support": {"source": "https://github.com/symfony/var-dumper/tree/v5.2.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-03-28T09:42:18+00:00"}, {"name": "symfony/var-exporter", "version": "v5.2.4", "source": {"type": "git", "url": "https://github.com/symfony/var-exporter.git", "reference": "5aed4875ab514c8cb9b6ff4772baa25fa4c10307"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-exporter/zipball/5aed4875ab514c8cb9b6ff4772baa25fa4c10307", "reference": "5aed4875ab514c8cb9b6ff4772baa25fa4c10307", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-php80": "^1.15"}, "require-dev": {"symfony/var-dumper": "^4.4.9|^5.0.9"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\VarExporter\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows exporting any serializable PHP data structure to plain PHP code", "homepage": "https://symfony.com", "keywords": ["clone", "construct", "export", "hydrate", "instantiate", "serialize"], "support": {"source": "https://github.com/symfony/var-exporter/tree/v5.2.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-01-27T10:01:46+00:00"}, {"name": "symfony/web-link", "version": "v5.2.5", "source": {"type": "git", "url": "https://github.com/symfony/web-link.git", "reference": "118ef73c177a033955af1342ec54f08dd1bf6d8e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/web-link/zipball/118ef73c177a033955af1342ec54f08dd1bf6d8e", "reference": "118ef73c177a033955af1342ec54f08dd1bf6d8e", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/link": "^1.0"}, "conflict": {"symfony/http-kernel": "<4.4"}, "provide": {"psr/link-implementation": "1.0"}, "require-dev": {"symfony/http-foundation": "^4.4|^5.0", "symfony/http-kernel": "^4.4|^5.0"}, "suggest": {"symfony/http-kernel": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\WebLink\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Manages links between resources", "homepage": "https://symfony.com", "keywords": ["dns-prefetch", "http", "http2", "link", "performance", "prefetch", "preload", "prerender", "psr13", "push"], "support": {"source": "https://github.com/symfony/web-link/tree/v5.2.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-03-07T15:51:33+00:00"}, {"name": "symfony/webpack-encore-bundle", "version": "v1.11.1", "source": {"type": "git", "url": "https://github.com/symfony/webpack-encore-bundle.git", "reference": "395b60a549ded8e7f77f0d551815d7555e2d9eb0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/webpack-encore-bundle/zipball/395b60a549ded8e7f77f0d551815d7555e2d9eb0", "reference": "395b60a549ded8e7f77f0d551815d7555e2d9eb0", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/asset": "^4.4 || ^5.0", "symfony/config": "^4.4 || ^5.0", "symfony/dependency-injection": "^4.4 || ^5.0", "symfony/http-kernel": "^4.4 || ^5.0", "symfony/service-contracts": "^1.0 || ^2.0"}, "require-dev": {"symfony/framework-bundle": "^4.4 || ^5.0", "symfony/phpunit-bridge": "^4.4 || ^5.0", "symfony/twig-bundle": "^4.4 || ^5.0", "symfony/web-link": "^4.4 || ^5.0"}, "type": "symfony-bundle", "extra": {"thanks": {"name": "symfony/webpack-encore", "url": "https://github.com/symfony/webpack-encore"}}, "autoload": {"psr-4": {"Symfony\\WebpackEncoreBundle\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Integration with your Symfony app & Webpack Encore!", "support": {"issues": "https://github.com/symfony/webpack-encore-bundle/issues", "source": "https://github.com/symfony/webpack-encore-bundle/tree/v1.11.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-02-17T13:45:51+00:00"}, {"name": "symfony/yaml", "version": "v5.2.5", "source": {"type": "git", "url": "https://github.com/symfony/yaml.git", "reference": "298a08ddda623485208506fcee08817807a251dd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/yaml/zipball/298a08ddda623485208506fcee08817807a251dd", "reference": "298a08ddda623485208506fcee08817807a251dd", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1", "symfony/polyfill-ctype": "~1.8"}, "conflict": {"symfony/console": "<4.4"}, "require-dev": {"symfony/console": "^4.4|^5.0"}, "suggest": {"symfony/console": "For validating YAML files using the lint command"}, "bin": ["Resources/bin/yaml-lint"], "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Yaml\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Loads and dumps YAML files", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/yaml/tree/v5.2.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-03-06T07:59:01+00:00"}, {"name": "twig/extra-bundle", "version": "v3.3.0", "source": {"type": "git", "url": "https://github.com/twigphp/twig-extra-bundle.git", "reference": "e2d27a86c3f47859eb07808fa7c8679d30fcbdde"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twigphp/twig-extra-bundle/zipball/e2d27a86c3f47859eb07808fa7c8679d30fcbdde", "reference": "e2d27a86c3f47859eb07808fa7c8679d30fcbdde", "shasum": ""}, "require": {"php": "^7.1.3|^8.0", "symfony/framework-bundle": "^4.3|^5.0", "symfony/twig-bundle": "^4.3|^5.0", "twig/twig": "^2.4|^3.0"}, "require-dev": {"symfony/phpunit-bridge": "^4.4.9|^5.0.9", "twig/cache-extra": "^3.0", "twig/cssinliner-extra": "^2.12|^3.0", "twig/html-extra": "^2.12|^3.0", "twig/inky-extra": "^2.12|^3.0", "twig/intl-extra": "^2.12|^3.0", "twig/markdown-extra": "^2.12|^3.0", "twig/string-extra": "^2.12|^3.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "3.2-dev"}}, "autoload": {"psr-4": {"Twig\\Extra\\TwigExtraBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://fabien.potencier.org", "role": "Lead Developer"}], "description": "A Symfony bundle for extra Twig extensions", "homepage": "https://twig.symfony.com", "keywords": ["bundle", "extra", "twig"], "support": {"source": "https://github.com/twigphp/twig-extra-bundle/tree/v3.3.0"}, "funding": [{"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/twig/twig", "type": "tidelift"}], "time": "2021-02-06T21:13:17+00:00"}, {"name": "twig/intl-extra", "version": "v3.3.0", "source": {"type": "git", "url": "https://github.com/twigphp/intl-extra.git", "reference": "919e8f945c30bd3efeb6a4d79722cda538116658"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twigphp/intl-extra/zipball/919e8f945c30bd3efeb6a4d79722cda538116658", "reference": "919e8f945c30bd3efeb6a4d79722cda538116658", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/intl": "^4.3|^5.0", "twig/twig": "^2.4|^3.0"}, "require-dev": {"symfony/phpunit-bridge": "^4.4.9|^5.0.9"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2-dev"}}, "autoload": {"psr-4": {"Twig\\Extra\\Intl\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://fabien.potencier.org", "role": "Lead Developer"}], "description": "A Twig extension for Intl", "homepage": "https://twig.symfony.com", "keywords": ["intl", "twig"], "support": {"source": "https://github.com/twigphp/intl-extra/tree/v3.3.0"}, "funding": [{"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/twig/twig", "type": "tidelift"}], "time": "2021-01-01T14:58:18+00:00"}, {"name": "twig/twig", "version": "v3.3.0", "source": {"type": "git", "url": "https://github.com/twigphp/Twig.git", "reference": "1f3b7e2c06cc05d42936a8ad508ff1db7975cdc5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twigphp/Twig/zipball/1f3b7e2c06cc05d42936a8ad508ff1db7975cdc5", "reference": "1f3b7e2c06cc05d42936a8ad508ff1db7975cdc5", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-ctype": "^1.8", "symfony/polyfill-mbstring": "^1.3"}, "require-dev": {"psr/container": "^1.0", "symfony/phpunit-bridge": "^4.4.9|^5.0.9"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.3-dev"}}, "autoload": {"psr-4": {"Twig\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://fabien.potencier.org", "role": "Lead Developer"}, {"name": "Twig Team", "role": "Contributors"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Project Founder"}], "description": "Twig, the flexible, fast, and secure template language for PHP", "homepage": "https://twig.symfony.com", "keywords": ["templating"], "support": {"issues": "https://github.com/twigphp/Twig/issues", "source": "https://github.com/twigphp/Twig/tree/v3.3.0"}, "funding": [{"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/twig/twig", "type": "tidelift"}], "time": "2021-02-08T09:54:36+00:00"}, {"name": "vierbergenlars/php-semver", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/vierbergenlars/php-semver.git", "reference": "be22b86be4c1133acc42fd1685276792024af5f9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/vierbergenlars/php-semver/zipball/be22b86be4c1133acc42fd1685276792024af5f9", "reference": "be22b86be4c1133acc42fd1685276792024af5f9", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "~4"}, "bin": ["bin/semver", "bin/update-versions"], "type": "library", "autoload": {"psr-0": {"vierbergenlars\\SemVer\\": "src/", "vierbergenlars\\LibJs\\": "src/"}, "classmap": ["src/vierbergenlars/SemVer/internal.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Semantic Versioner for PHP", "keywords": ["semantic", "semver", "versioning"], "support": {"issues": "https://github.com/vierbergenlars/php-semver/issues", "source": "https://github.com/vierbergenlars/php-semver/tree/master"}, "time": "2017-07-11T09:53:59+00:00"}, {"name": "webmozart/assert", "version": "1.10.0", "source": {"type": "git", "url": "https://github.com/webmozarts/assert.git", "reference": "6964c76c7804814a842473e0c8fd15bab0f18e25"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozarts/assert/zipball/6964c76c7804814a842473e0c8fd15bab0f18e25", "reference": "6964c76c7804814a842473e0c8fd15bab0f18e25", "shasum": ""}, "require": {"php": "^7.2 || ^8.0", "symfony/polyfill-ctype": "^1.8"}, "conflict": {"phpstan/phpstan": "<0.12.20", "vimeo/psalm": "<4.6.1 || 4.6.2"}, "require-dev": {"phpunit/phpunit": "^8.5.13"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.10-dev"}}, "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "support": {"issues": "https://github.com/webmozarts/assert/issues", "source": "https://github.com/webmozarts/assert/tree/1.10.0"}, "time": "2021-03-09T10:59:23+00:00"}, {"name": "will<PERSON><PERSON>/negotiation", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/willdurand/Negotiation.git", "reference": "04e14f38d4edfcc974114a07d2777d90c98f3d9c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/willdurand/Negotiation/zipball/04e14f38d4edfcc974114a07d2777d90c98f3d9c", "reference": "04e14f38d4edfcc974114a07d2777d90c98f3d9c", "shasum": ""}, "require": {"php": ">=7.1.0"}, "require-dev": {"symfony/phpunit-bridge": "^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"psr-4": {"Negotiation\\": "src/Negotiation"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Content Negotiation tools for PHP provided as a standalone library.", "homepage": "http://williamdurand.fr/Negotiation/", "keywords": ["accept", "content", "format", "header", "negotiation"], "support": {"issues": "https://github.com/willdurand/Negotiation/issues", "source": "https://github.com/willdurand/Negotiation/tree/3.0.0"}, "time": "2020-09-25T08:01:41+00:00"}], "packages-dev": [{"name": "nikic/php-parser", "version": "v4.10.4", "source": {"type": "git", "url": "https://github.com/nikic/PHP-Parser.git", "reference": "c6d052fc58cb876152f89f532b95a8d7907e7f0e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/PHP-Parser/zipball/c6d052fc58cb876152f89f532b95a8d7907e7f0e", "reference": "c6d052fc58cb876152f89f532b95a8d7907e7f0e", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=7.0"}, "require-dev": {"ircmaxell/php-yacc": "^0.0.7", "phpunit/phpunit": "^6.5 || ^7.0 || ^8.0 || ^9.0"}, "bin": ["bin/php-parse"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.9-dev"}}, "autoload": {"psr-4": {"PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "support": {"issues": "https://github.com/nikic/PHP-Parser/issues", "source": "https://github.com/nikic/PHP-Parser/tree/v4.10.4"}, "time": "2020-12-20T10:01:03+00:00"}, {"name": "symfony/maker-bundle", "version": "v1.30.2", "source": {"type": "git", "url": "https://github.com/symfony/maker-bundle.git", "reference": "a395a85aa4ded6c1fa3da118d60329b64b6c2acd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/maker-bundle/zipball/a395a85aa4ded6c1fa3da118d60329b64b6c2acd", "reference": "a395a85aa4ded6c1fa3da118d60329b64b6c2acd", "shasum": ""}, "require": {"doctrine/inflector": "^1.2|^2.0", "nikic/php-parser": "^4.0", "php": ">=7.1.3", "symfony/config": "^4.0|^5.0", "symfony/console": "^4.0|^5.0", "symfony/dependency-injection": "^4.0|^5.0", "symfony/deprecation-contracts": "^2.2", "symfony/filesystem": "^4.0|^5.0", "symfony/finder": "^4.0|^5.0", "symfony/framework-bundle": "^4.0|^5.0", "symfony/http-kernel": "^4.0|^5.0"}, "require-dev": {"composer/semver": "^3.0@dev", "doctrine/doctrine-bundle": "^1.8|^2.0", "doctrine/orm": "^2.3", "friendsofphp/php-cs-fixer": "^2.8", "friendsoftwig/twigcs": "^4.1.0|^5.0.0", "symfony/http-client": "^4.3|^5.0", "symfony/phpunit-bridge": "^4.3|^5.0", "symfony/process": "^4.0|^5.0", "symfony/security-core": "^4.0|^5.0", "symfony/yaml": "^4.0|^5.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-main": "1.0-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\MakerBundle\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Maker helps you create empty commands, controllers, form classes, tests and more so you can forget about writing boilerplate code.", "homepage": "https://symfony.com/doc/current/bundles/SymfonyMakerBundle/index.html", "keywords": ["code generator", "generator", "scaffold", "scaffolding"], "support": {"issues": "https://github.com/symfony/maker-bundle/issues", "source": "https://github.com/symfony/maker-bundle/tree/v1.30.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-03-23T13:53:38+00:00"}, {"name": "symfony/phpunit-bridge", "version": "v5.2.6", "source": {"type": "git", "url": "https://github.com/symfony/phpunit-bridge.git", "reference": "f2f94fd78379cdcdef09dd5025af791301913968"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/phpunit-bridge/zipball/f2f94fd78379cdcdef09dd5025af791301913968", "reference": "f2f94fd78379cdcdef09dd5025af791301913968", "shasum": ""}, "require": {"php": ">=5.5.9"}, "conflict": {"phpunit/phpunit": "<4.8.35|<5.4.3,>=5.0|<6.4,>=6.0|9.1.2"}, "require-dev": {"symfony/deprecation-contracts": "^2.1", "symfony/error-handler": "^4.4|^5.0"}, "suggest": {"symfony/error-handler": "For tracking deprecated interfaces usages at runtime with DebugClassLoader"}, "bin": ["bin/simple-phpunit"], "type": "symfony-bridge", "extra": {"thanks": {"name": "phpunit/phpunit", "url": "https://github.com/sebastian<PERSON>mann/phpunit"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Bridge\\PhpUnit\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides utilities for PHPUnit, especially user deprecation notices management", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/phpunit-bridge/tree/v5.2.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-03-23T20:42:04+00:00"}, {"name": "symfony/profiler-pack", "version": "v1.0.5", "source": {"type": "git", "url": "https://github.com/symfony/profiler-pack.git", "reference": "29ec66471082b4eb068db11eb4f0a48c277653f7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/profiler-pack/zipball/29ec66471082b4eb068db11eb4f0a48c277653f7", "reference": "29ec66471082b4eb068db11eb4f0a48c277653f7", "shasum": ""}, "require": {"symfony/stopwatch": "*", "symfony/twig-bundle": "*", "symfony/web-profiler-bundle": "*"}, "type": "symfony-pack", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A pack for the Symfony web profiler", "support": {"issues": "https://github.com/symfony/profiler-pack/issues", "source": "https://github.com/symfony/profiler-pack/tree/v1.0.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-08-12T06:50:46+00:00"}, {"name": "symfony/web-profiler-bundle", "version": "v5.2.6", "source": {"type": "git", "url": "https://github.com/symfony/web-profiler-bundle.git", "reference": "58e5be2aa69041ff35250537190d9ec29136782a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/web-profiler-bundle/zipball/58e5be2aa69041ff35250537190d9ec29136782a", "reference": "58e5be2aa69041ff35250537190d9ec29136782a", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/config": "^4.4|^5.0", "symfony/framework-bundle": "^5.1", "symfony/http-kernel": "^5.2", "symfony/routing": "^4.4|^5.0", "symfony/twig-bundle": "^4.4|^5.0", "twig/twig": "^2.13|^3.0.4"}, "conflict": {"symfony/dependency-injection": "<5.2", "symfony/form": "<4.4", "symfony/messenger": "<4.4"}, "require-dev": {"symfony/browser-kit": "^4.4|^5.0", "symfony/console": "^4.4|^5.0", "symfony/css-selector": "^4.4|^5.0", "symfony/stopwatch": "^4.4|^5.0"}, "type": "symfony-bundle", "autoload": {"psr-4": {"Symfony\\Bundle\\WebProfilerBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a development tool that gives detailed information about the execution of any request", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/web-profiler-bundle/tree/v5.2.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-03-16T09:10:13+00:00"}], "aliases": [], "minimum-stability": "stable", "stability-flags": [], "prefer-stable": false, "prefer-lowest": false, "platform": {"php": "^7.4", "ext-ctype": "*", "ext-iconv": "*"}, "platform-dev": [], "plugin-api-version": "2.0.0"}