<?php

declare(strict_types=1);

namespace App\OpenApi;

use ApiPlatform\Core\OpenApi\Factory\OpenApiFactoryInterface;
use ApiPlatform\Core\OpenApi\Model\Paths;
use ApiPlatform\Core\OpenApi\OpenApi;
use ApiPlatform\Core\OpenApi\Model\PathItem;

final class SwaggerDecorator implements OpenApiFactoryInterface
{
    private OpenApiFactoryInterface $decorated;
    private Paths $newPaths;

    public function __construct(OpenApiFactoryInterface $decorated, Paths $newPaths)
    {
        $this->decorated = $decorated;
        $this->newPaths = $newPaths;
    }

    public function __invoke(array $context = []): OpenApi
    {
        $openApi = ($this->decorated)($context);
        $paths = $openApi->getPaths()->getPaths();

        $paths = $this->addForecastRoutes($paths);
        $paths = $this->addPlaceRoutes($paths);
        $paths = $this->addUserRoutes($paths);
        $paths = $this->addClientRoutes($paths);
        $paths = $this->removeHealthcheckRoutes($paths);

        foreach ($paths as $pathKey => $pathItem) {
            $this->newPaths->addPath($pathKey, $pathItem);
        }

        $openApi = $openApi->withPaths($this->newPaths);

        return $openApi;
    }

    /**
     * Use custom definition for Forecast routes
     * @param array $paths
     * @return array
     */
    private function addForecastRoutes(array $paths): array
    {
        unset($paths['/bff/api/forecasts/{available}']);

        $getOperation = $paths['/bff/api/forecasts']->getGet()->withParameters(ForecastDefinition::getGetParameters());
        $pathItem = new PathItem();
        $clone = $pathItem->withGet($getOperation);
        $paths['/bff/api/forecasts'] = $clone;

        return $paths;
    }

    /**
     * Use custom definition for Places routes
     * @param array $paths
     * @return array
     */
    private function addPlaceRoutes(array $paths): array
    {
        unset($paths['/bff/api/places/{id}']);

        $getOperation = $paths['/bff/api/places']->getGet()->withParameters(PlaceDefinition::getGetParameters());
        $pathItem = new PathItem();
        $clone = $pathItem->withGet($getOperation);
        $paths['/bff/api/places'] = $clone;

        return $paths;
    }

    /**
     * Use custom definition for Client routes
     * @param array $paths
     * @return array
     */
    private function addClientRoutes(array $paths): array
    {
        unset($paths['/bff/api/clients/{id}']);

        $getOperation = ClientDefinition::getGetItemOperation();

        $pathItem = new PathItem();
        $clone = $pathItem->withGet($getOperation);
        $paths['/bff/api/clients/{clientId}'] = $clone;

        $getCollection = $paths['/bff/api/clients']->getGet()->withParameters(ClientDefinition::getGetParameters());
        $pathItem = new PathItem();
        $clone = $pathItem->withGet($getCollection);
        $paths['/bff/api/clients'] = $clone;

        return $paths;
    }

    private function addUserRoutes(array $paths): array
    {
        $getOperation = UserDefinition::getGetSelfOperation();

        $pathItem = new PathItem();
        $clone = $pathItem->withGet($getOperation);
        $paths['/bff/api/users/me'] = $clone;

        return $paths;
    }

    /**
     * Hide healthcheck routes from swagger
     * @param array $paths
     * @return array
     */
    private function removeHealthcheckRoutes(array $paths): array
    {
        unset(
            $paths['/bff/api/healthcheck/liveness/{id}'],
            $paths['/bff/api/healthcheck/readiness/{id}'],
            $paths['/bff/api/healthcheck/liveness'],
            $paths['/bff/api/healthcheck/readiness'],
        );

        return $paths;
    }
}
