<?php

declare(strict_types=1);

namespace App\DataProvider;

use ApiPlatform\Core\DataProvider\CollectionDataProviderInterface;
use ApiPlatform\Core\DataProvider\RestrictedDataProviderInterface;
use ApiPlatform\Core\Exception\RuntimeException;
use Symfony\Component\HttpFoundation\RequestStack;
use App\Entity\Forecast;
use App\Hexagonal\Forecast\Adapters\ForecastApiRequest;
use App\Hexagonal\Forecast\Adapters\ForecastApiService;

final class ForecastCollectionDataProvider implements CollectionDataProviderInterface, RestrictedDataProviderInterface
{
    private RequestStack $requestStack;
    private ForecastApiRequest $forecastApiRequest;
    private ForecastApiService $forecastApiService;

    public function __construct(
        RequestStack $requestStack,
        ForecastApiRequest $forecastApiRequest,
        ForecastApiService $forecastApiService
    )
    {
        $this->requestStack = $requestStack;
        $this->forecastApiRequest = $forecastApiRequest;
        $this->forecastApiService = $forecastApiService;
    }

    public function supports(string $resourceClass, string $operationName = null, array $context = []): bool
    {
        return Forecast::class === $resourceClass;
    }

    public function getCollection(string $resourceClass, string $operationName = null): \Generator
    {
        $query = $this->requestStack->getCurrentRequest()->query;
        $requestParameters = $query->all();

        $this->forecastApiRequest->setRequest($requestParameters);
        if (!$this->forecastApiRequest->isValid()) {
            throw new RuntimeException($this->forecastApiRequest->getErrors());
        }

        $forecastResults = $this->forecastApiService->get($this->forecastApiRequest);

        foreach ($forecastResults as $forecastResult) {
            yield $forecastResult;
        }
    }
}
