<?php

declare(strict_types=1);

namespace App\Hexagonal\Count\Domain\Validator;

use App\Entity\Count;
use App\Entity\User;

interface CountValidatorPort
{
    /**
     * Get the value of errorMessage
     * @return string
     */
    public function getErrorMessage(): string;

    /**
     * Set the value of errorMessage
     * @param string $errorMessage
     */
    public function setErrorMessage(string $errorMessage): void;

    /**
     * Check for errors in post count
     * @param Count $count
     * @param User $user
     * @return bool
     */
    public function isPostValid(Count $count, User $user): bool;

    /**
     * Check for errors in patch count
     * @param Count $count
     * @param User $user
     * @return bool
     */
    public function isPatchValid(Count $count, User $user): bool;
}