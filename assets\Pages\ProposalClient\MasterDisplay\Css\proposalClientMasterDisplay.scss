.proposalClient {
  @import "../../ProposalHeader/proposalHeader";

  font-family: $font-sourceSansPro;

  .wrapperCountsResult {
    position: relative;

    .mapsCounts {
      position: absolute;
      left: 0;
      height: 25em;
      width: 100%;
      background-color: $lighterSaphir;
      background-size: cover;
    }

    .contentCountsResult {
      padding: 0em 10em 2.5em 10em;
      position: absolute;
      width: 100%;

      @include middleDesktop() {
        padding: 0em 4em 2.5em 4em;
      }

      h3 {
        font-size: 2em;
        color: $black;
        font-family: $font-sourceSansPro;
      }

      .wrapperSummaryAndText {
        height: 34em;
        display: flex;
        align-items: flex-end;
        justify-content: space-between;

        .text {
          width: 49%;

          p:first-child {
            font-size: 2em;
            color: $black;
            font-weight: 700;
          }

          p:last-child {
            color: $hoverMenu;
            margin-top: 0.875em;
          }
        }
      }

      @import "../ProposalTableCounts/Css/proposalTableCounts";

      .generateText {
        display: flex;
        justify-content: center;
        margin-top: 6.25em;

        p {
          font-size: 1.5em;
          color: $black;
          @include middleDesktop() {
            font-size: 1.25em;
          }
        }
      }

      .endText {
        margin-top: 18.75em;
        display: flex;
        justify-content: center;

        p {
          color: $grey;
        }
      }

      .logo {
        margin-top: 1em;
        display: flex;
        justify-content: center;
        height: 3em;
      }
    }

    @keyframes lazyloadCountResult {
      0% {
        background-position: -5em 0;
      }

      100% {
        background-position: 10em 0;
      }
    }
  }
}
