<?php

declare(strict_types=1);

namespace App\DataProvider;

use ApiPlatform\Core\DataProvider\ItemDataProviderInterface;
use ApiPlatform\Core\DataProvider\RestrictedDataProviderInterface;
use ApiPlatform\Core\Exception\RuntimeException;
use App\Hexagonal\Client\Domain\ClientService;
use App\Hexagonal\Forecast\Adapters\ForecastApiRequest;
use App\Hexagonal\Forecast\Adapters\ForecastApiService;
use Symfony\Component\HttpFoundation\RequestStack;
use App\Hexagonal\Client\Domain\ClientRepositoryPort;
use App\Entity\Client;

final class ClientItemDataProvider implements ItemDataProviderInterface, RestrictedDataProviderInterface
{
    private ClientRepositoryPort $clientRepository;
    private RequestStack $requestStack;
    private ForecastApiRequest $forecastApiRequest;
    private ForecastApiService $forecastApiService;
    private ClientService $clientService;

    public function __construct(ClientRepositoryPort $clientRepository,
                                RequestStack $requestStack,
                                ForecastApiRequest $forecastApiRequest,
                                ForecastApiService $forecastApiService,
                                ClientService $clientService)
    {
        $this->clientRepository = $clientRepository;
        $this->requestStack = $requestStack;
        $this->forecastApiRequest = $forecastApiRequest;
        $this->forecastApiService = $forecastApiService;
        $this->clientService = $clientService;
    }

    public function supports(string $resourceClass, string $operationName = null, array $context = []): bool
    {
        return Client::class === $resourceClass;
    }

    public function getItem(string $resourceClass, $id, string $operationName = null, array $context = []): ?Client
    {
        $requestParameters = $this->requestStack->getCurrentRequest()->query->all();
        $explodedQuery = explode('/', $requestParameters['q']);
        $clientId = end($explodedQuery);

        // client with filtered intensity
        $this->forecastApiRequest->setRequest($requestParameters);
        if (!$this->forecastApiRequest->isValid()) {
            throw new RuntimeException($this->forecastApiRequest->getErrors());
        }

        if (!$this->clientRepository->getByClientId($clientId)) {
            throw new RuntimeException('Unknown client id.');
        }

        if (intval($requestParameters['productId']) !== 2) {
            throw new RuntimeException('Product id must be 2.');
        }

        $forecastResults = $this->forecastApiService->get($this->forecastApiRequest);

        return $this->clientService->getByClientIdAndForecast($clientId, $forecastResults);
    }
}
