{% extends "base.html.twig" %}

{% block title %}MyAdFactory - Groupe SeLoger{% endblock %}
{% block stylesheets %}
    {{ encore_entry_link_tags('app') }}
{% endblock %}

{% block body %}
    <div id="root">
        <div class="layout" style="font-size: 16px;">
            <div class="proposalClient">
                <div class="proposalHeader">
                    <div class="logo"><img src="/images/logo_groupe.svg" alt="Logo"></div>
                    <div class="wrapperCodeMaf">
                        <div class="codeMaf">
                            <div class="contentCodeMaf"><p>MAF-{{ count.id }}</p></div>
                        </div>
                    </div>
                </div>
                <div class="wrapperCountsResult">
                    <div class="mapsCounts" style="background-image: url(&quot;/images/map_background.jpg&quot;);"></div>
                    <div class="contentCountsResult">
                        <div class="wrapperSummaryAndText">
                            <div class="text"><p>Bonjour,</p>
                                <p>Vous trouverez ci-dessous le plan média répondant aux besoins évoqués ensemble. Nous sommes à votre
                                    écoute
                                    pour toute question complémentaire.</p></div>
                            <div class="contentSummary">
                                <div class="broadcastMedium">
                                    {% for broadcastMedium in count.broadcastMedium %}
                                        {% if broadcastMedium == 'seloger' %}
                                            <div class="medium"
                                                 style="background-image: url(&quot;/images/seLoger.svg&quot;);"></div>
                                        {% else %}
                                            <div class="medium"
                                                 style="background-image: url(&quot;/images/logicImmo.svg&quot;);"></div>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                                <div class="priceAndPrinting">
                                    <div class="price"> {{ count.totalBudget|format_number(locale='fr') }} €</div>
                                    <div class="printing">{{ count.totalImpPurchased|format_number(locale='fr') }}
                                        Impressions <br>/
                                        mois sur
                                        l’année
                                    </div>
                                </div>
                                <div class="productName">{{ count.productName }}</div>
                                <div class="tableInformations">
                                    <div class="display">
                                        <div class="icon">
                                            <div data-testid="gsl.uilib.Icon" class="sc-bdfBwQ fQfaqo">
                                                <svg width="1em" height="1em" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"
                                                     xmlns:xlink="http://www.w3.org/1999/xlink" role="img">
                                                    <defs>
                                                        <path d="M17.127 1c1.17.174 1.993 1.266 1.858 2.465v9.531c.136 1.195-.681 2.285-1.848 2.465l-5.014-.001.942 2.475h2.39c.284 0 .516.236.516.527 0 .291-.232.527-.517.527H7.288a.395.395 0 01-.186 0H4.604a.521.521 0 01-.517-.527c0-.29.231-.526.517-.526l2.352-.001.942-2.475H2.879c-1.17-.174-1.993-1.265-1.858-2.464V3.517C.855 2.3 1.686 1.174 2.879 1h14.248zm-6.108 14.46H9.003l-.941 2.475h3.899l-.942-2.475zm6.933-3.423H2.053v.96c0 .831.434 1.41.826 1.41h14.258c.382 0 .816-.579.816-1.41l-.001-.96zM2.879 2.053c-.392 0-.826.58-.826 1.412v7.519h15.899V3.517c0-.832-.433-1.411-.825-1.411L2.879 2.053z"
                                                              id="desktop-lined_svg__a"></path>
                                                    </defs>
                                                    <use fill="currentColor" xlink:href="#desktop-lined_svg__a" fill-rule="evenodd"></use>
                                                </svg>
                                            </div>
                                        </div>
                                        <div class="contentBloc">
                                            <div class="title">Appareils</div>
                                            <div class="description">Desktop, tablette, mobile</div>
                                        </div>
                                    </div>
                                    <div class="projectType">
                                        <div class="icon">
                                            <div data-testid="gsl.uilib.Icon" class="sc-bdfBwQ fQfaqo">
                                                <svg width="1em" height="1em" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"
                                                     xmlns:xlink="http://www.w3.org/1999/xlink" role="img">
                                                    <defs>
                                                        <path d="M14.605 1c1.047 0 1.895.859 1.895 1.918v14.164a1.93 1.93 0 01-.57 1.37c-.364.362-.856.559-1.366.548H5.395A1.906 1.906 0 013.5 17.082V2.918c0-.516.205-1.01.57-1.37.364-.362.856-.559 1.366-.548h9.169zm-.04 1.031h-9.17a.892.892 0 00-.876.887v14.164a.892.892 0 00.876.887h9.17c.48-.006.87-.4.875-.887V2.918a.892.892 0 00-.876-.887zm-2.996 12c.281 0 .51.23.51.515a.512.512 0 01-.51.516H6.607a.512.512 0 01-.509-.516c0-.285.228-.515.51-.515h4.961zm1.824-3.392c.281 0 .509.23.509.516a.512.512 0 01-.51.515H6.608a.512.512 0 01-.509-.515c0-.285.228-.516.51-.516h6.785zm.509-6.175v3.814H6.098V4.464h7.804zm-1.019 1.031H7.117v1.753h5.766V5.495z"
                                                              id="document-lined_svg__a"></path>
                                                    </defs>
                                                    <use fill="currentColor" xlink:href="#document-lined_svg__a" fill-rule="evenodd"></use>
                                                </svg>
                                            </div>
                                        </div>
                                        <div class="contentBloc">
                                            <div class="title">Type de projet</div>
                                            <div class="description">
                                                {{ count.projectType|join(', ') }}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="reccuring">
                                        <div class="icon">
                                            <div data-testid="gsl.uilib.Icon" class="sc-bdfBwQ fQfaqo">
                                                <svg width="1em" height="1em" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"
                                                     xmlns:xlink="http://www.w3.org/1999/xlink" role="img">
                                                    <defs>
                                                        <path d="M15.845 1c.285 0 .516.229.516.51l-.001.93h.856c.985 0 1.784.792 1.784 1.768v13.025c.003.47-.184.92-.52 1.252-.334.332-.79.518-1.264.515H2.784a1.782 1.782 0 01-1.265-.515A1.75 1.75 0 011 17.233V4.208c0-.976.799-1.768 1.784-1.768h.855v-.93c0-.281.231-.51.516-.51.284 0 .515.229.515.51v.93h2.866v-.93c0-.281.23-.51.516-.51.284 0 .515.229.515.51v.93h2.865l.001-.93c0-.281.23-.51.515-.51.285 0 .516.229.516.51l-.001.93h2.866v-.93c0-.281.232-.51.516-.51zm2.124 5.342H2.03l.001 10.89a.75.75 0 00.753.746h14.432a.75.75 0 00.753-.745V6.342zm-13 7.703a.79.79 0 01.794.787.79.79 0 01-.794.787.79.79 0 01-.794-.787.79.79 0 01.794-.787zm3.35 0a.79.79 0 01.794.787.79.79 0 01-.793.787.79.79 0 01-.794-.787.79.79 0 01.794-.787zm3.361 0a.79.79 0 01.794.787.79.79 0 01-.794.787.79.79 0 01-.793-.787.79.79 0 01.793-.787zm-6.71-3.044a.79.79 0 01.793.787.79.79 0 01-.794.786.79.79 0 01-.794-.786A.79.79 0 014.97 11zm3.35 0a.79.79 0 01.793.787.79.79 0 01-.793.786.79.79 0 01-.794-.786A.79.79 0 018.32 11zm3.36 0a.79.79 0 01.794.787.79.79 0 01-.794.786.79.79 0 01-.793-.786.79.79 0 01.793-.787zm3.35 0a.79.79 0 01.795.787.79.79 0 01-.794.786.79.79 0 01-.794-.786.79.79 0 01.794-.787zM8.32 7.967a.79.79 0 01.793.787.79.79 0 01-.793.786.79.79 0 01-.794-.786.79.79 0 01.794-.787zm3.36 0a.79.79 0 01.794.787.79.79 0 01-.794.786.79.79 0 01-.793-.786.79.79 0 01.793-.787zm3.35 0a.79.79 0 01.795.787.79.79 0 01-.794.786.79.79 0 01-.794-.786.79.79 0 01.794-.787zM3.64 3.461l-.855.001a.75.75 0 00-.753.746L2.03 5.32h15.939V4.208a.75.75 0 00-.753-.746l-.856-.001v.634c0 .282-.23.511-.515.511a.523.523 0 01-.515-.51l-.001-.635h-2.866v.634c0 .282-.23.511-.515.511a.513.513 0 01-.515-.51l-.001-.635H8.567v.634c0 .282-.23.511-.515.511a.513.513 0 01-.516-.51V3.46H4.67v.634a.523.523 0 01-.515.511.513.513 0 01-.516-.51V3.46z"
                                                              id="calendar-lined_svg__a"></path>
                                                    </defs>
                                                    <use fill="currentColor" xlink:href="#calendar-lined_svg__a" fill-rule="evenodd"></use>
                                                </svg>
                                            </div>
                                        </div>
                                        <div class="contentBloc">
                                            <div class="title">Périodicité</div>
                                            <div class="description">{{ count.periodicity }}</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="location">
                                    <div class="locationTitle">Localité(s) recherché(es) par la cible</div>
                                    <div class="contentLocation">
                                        {% for countItem in count.countItem %}
                                            <div><span>{{ countItem.name }} ({{ countItem.code }})</span></div>
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="proposalTableCounts">
                            <div class="wrapperTable">
                                <div class="titleTable">
                                    <div>
                                        <div class="locationRow"><p>Localités</p></div>
                                    </div>
                                    <div class="blue"><p>Impressions proposées <span>(par mois)</span></p></div>
                                    <div class="blue"><p>Budgets <span>(hors taxe)</span></p></div>
                                </div>
                                <div class="wrapperLocationTable">
                                    {% set totalPrint = 0 %}
                                    {% for countItem in count.countItem %}

                                        {% set totalPrint = totalPrint + countItem.sold + countItem.available %}
                                        {% set coverage = (countItem.budget / 125 * 1000 / (countItem.sold + countItem.available)) %}

                                        <div class="locationTable">
                                            <div><p>{{ countItem.name }} ({{ countItem.code }})</p></div>
                                            <div><p>{{ countItem.purchased|format_number(locale='fr') }} Imp. /
                                                    mois<span>Soit {{ coverage|format_number(style='percent') }} de
                                                        couverture</span>
                                                </p></div>
                                            <div class="wrapperBudget">
                                                <div><p>{{ countItem.budget|format_number(locale='fr') }} €</p><span>/ hors taxe</span>
                                                </div>
                                            </div>
                                        </div>
                                    {% endfor %}
                                </div>
                                <div class="tableResult">
                                    <div><p class="titleTotalCounts">Total du comptage:</p></div>
                                    <div><p>{{ count.totalImpPurchased|format_number(locale='fr') }} Imp. /
                                            mois<span>Soit {{ (count.totalImpPurchased / totalPrint)|format_number(style='percent') }} de
                                                couverture</span></p></div>
                                    <div>
                                        <div class="totalBudget"><p>{{ count.totalBudget|format_number(locale='fr') }} €</p><span>/ hors taxe</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="endText"><p>Proposition réalisée
                                le {{ count.createdDate|format_datetime('short', 'none', locale='fr') }} par</p></div>
                        <div class="logo"><img src="/images/logo_blue.svg" alt="Logo"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}
