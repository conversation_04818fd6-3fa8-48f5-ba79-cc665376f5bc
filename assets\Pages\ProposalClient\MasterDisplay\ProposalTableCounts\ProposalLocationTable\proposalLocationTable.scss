.wrapperLocationTable {
  .locationTable {
    display: grid;
    grid-template-columns: 33.4% 33.3% 33.3%;

    .loadingBarsSold,
    .loadingBarsAvailable,
    .loadingBarsPrint {
      .firstBarLoading {
        width: 4.313em;
        height: 1.375em;
        border-radius: 0.25em;
        margin-bottom: 0.25em;
      }

      .secondBarLoading {
        width: 7.5em;
        height: 1em;
        border-radius: 0.25em;
      }

      .firstBarLoading,
      .secondBarLoading {
        animation-duration: 1s;
        animation-fill-mode: forwards;
        animation-iteration-count: infinite;
        animation-name: lazyLoadTableCustom;
        animation-timing-function: linear;
        animation-timing-function: linear;
        background: $lighterSaphir;
        background-size: 8em 1.4em;
        background-image: linear-gradient(
          to right,
          $lighterSaphir 0%,
          $ghostWhite 20%,
          $lighterSaphir 40%,
          $lighterSaphir 100%
        );
      }

      @include middleDesktop() {
        .secondBarLoading {
          width: 6em;
        }
      }
    }

    .wrapperBudget {
      > div {
        display: flex;
        align-items: center;

        p {
          font-size: 1.125em;
          font-weight: 600;
        }
        span {
          font-size: 0.75em;
          color: $grey;
          margin-left: 0.75em;
        }
      }
      .loadingBarMaxBudget {
        animation-duration: 1s;
        animation-fill-mode: forwards;
        animation-iteration-count: infinite;
        animation-name: lazyLoadTableCustom;
        animation-timing-function: linear;
        animation-timing-function: linear;
        background: $lighterSaphir;
        background-size: 4.75em 1.1em;
        background-image: linear-gradient(
          to right,
          $lighterSaphir 0%,
          $ghostWhite 20%,
          $lighterSaphir 40%,
          $lighterSaphir 100%
        );
        width: 4.75em;
        height: 1em;
        border-radius: 0.25em;
        margin-bottom: 0.25em;
      }
      .maximumBudget {
        font-size: 0.875em;
        color: $grey;
        font-family: $font-sourceSansPro;
        font-weight: 500;
      }

      .wrapperInput {
        margin-top: 0.5em;
        display: flex;
        height: 2.5em;

        .loadingBarInput {
          animation-duration: 1s;
          animation-fill-mode: forwards;
          animation-iteration-count: infinite;
          animation-name: lazyLoadTableCustom;
          animation-timing-function: linear;
          animation-timing-function: linear;
          background: $lighterSaphir;
          background-size: 15.75em 2.69em;
          background-image: linear-gradient(
            to right,
            $lighterSaphir 0%,
            $ghostWhite 20%,
            $lighterSaphir 40%,
            $lighterSaphir 100%
          );
          width: 15.75em;
          height: 2.69em;
          border-radius: 0.25em;
          margin-bottom: 1.375em;
        }
        input {
          border-radius: 0.3em;
          border-top-right-radius: 0;
          border-bottom-right-radius: 0;
          height: 100%;
          width: 100%;
          border: 1px solid $lightGrey;
          padding: 0 1em 0 1em;
          outline: none !important;
          border-right: none;
        }
        span {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 3em;
          border: 1px solid $lightGrey;
          border-radius: 0.3em;
          border-top-left-radius: 0;
          border-bottom-left-radius: 0;
          border-left: none;
        }
      }
      .errorBudget {
        font-size: 1em;
        color: $error;
        font-family: $font-sourceSansPro;
        font-weight: 500;
      }
    }

    > div {
      padding: 1em 1.25em;
      display: flex;
      align-items: center;
      border-top: 1px solid $lighterGrey;
      border-left: 1px solid $lighterGrey;
      &:nth-child(3n) {
        border-right: 1px solid $darkerSaphir;
      }
      &:nth-child(2n) {
        border-left: 1px solid $darkerSaphir;
      }
      p {
        span {
          font-size: 0.75em;
          display: block;
        }
      }
    }
    &:first-child {
      > div {
        &:nth-child(3n) {
          border-top: 1px solid $darkerSaphir;
        }
        &:nth-child(2n) {
          border-top: 1px solid $darkerSaphir;
        }
      }
    }
    &:last-child {
      > div {
        &:nth-child(1) {
          border-bottom-left-radius: 0.3em;
        }
        border-bottom: 1px solid $lighterGrey;
      }
    }
  }
  @keyframes lazyLoadTableCustom {
    0% {
      background-position: -15em 0;
    }

    100% {
      background-position: 15em 0;
    }
  }
}
