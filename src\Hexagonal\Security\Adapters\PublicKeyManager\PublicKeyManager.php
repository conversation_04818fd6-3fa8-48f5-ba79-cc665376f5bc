<?php

declare(strict_types=1);

namespace App\Hexagonal\Security\Adapters\PublicKeyManager;

use Symfony\Contracts\HttpClient\HttpClientInterface;
use CoderCat\JWKToPEM\JWKConverter;
use App\Hexagonal\Security\Domain\PublicKeyManager\PublicKeyManagerPort;

final class PublicKeyManager implements PublicKeyManagerPort
{
    private string $keyUrl;
    private string $pathToKey;
    private HttpClientInterface $httpClient;
    private JWKConverter $jwkConverter;

    public function __construct(
        string $keyUrl,
        string $pathToKey,
        HttpClientInterface $httpClient,
        JWKConverter $jwkConverter
    ) {
        $this->keyUrl = $keyUrl;
        $this->pathToKey = $pathToKey;
        $this->httpClient = $httpClient;
        $this->jwkConverter = $jwkConverter;
    }

    /**
     * @inheritdoc
     */
    public function fetch(string $kid): ?string
    {
        // Use the cache to verify the token
        $matchingCachedKey = $this->getKeyByKid($kid, $this->getKeysFromCache());

        // If cache is outdated, renew the cache
        if (empty($matchingCachedKey)) {
            $this->setCache();
            $matchingCachedKey = $this->getKeyByKid($kid, $this->getKeysFromCache());
        }

        // Still empty => error
        if (empty($matchingCachedKey)) {
            return null;
        }

        $pem = $this->jwkConverter->toPEM($matchingCachedKey);

        return $pem;
    }

    /**
     * Retrieve cached keys
     * @return array
     */
    private function getKeysFromCache(): array
    {
        // If no cache, retrieve from Keycloak and set cache
        if (empty($this->getCache())) {
            $this->setCache();
        }

        return $this->getCache()['keys'];
    }

    /**
     * Retrieve locally cached file as array
     * @return array
     */
    private function getCache(): array
    {
        if (!file_exists($this->pathToKey)) {
            return [];
        }

        $keys = file_get_contents($this->pathToKey);
        return json_decode($keys, true);
    }

    /**
     * Save public keys from remote Keycloak server for offline validation
     */
    private function setCache(): void
    {
        $response = $this->httpClient->request('GET', $this->keyUrl);

        // Locally save the keys
        file_put_contents($this->pathToKey, $response->getContent());
    }

    /**
     * Get key by $kid from $keys
     * @param string $kid
     * @param array $keys
     * @return array
     */
    private function getKeyByKid(string $kid, array $keys): array
    {
        $matchingKey = array_values(array_filter($keys, function ($key) use ($kid) {
            return $key['kid'] === $kid;
        }));

        if(empty($matchingKey)){
            return [];
        }

        return $matchingKey[0];
    }
}
