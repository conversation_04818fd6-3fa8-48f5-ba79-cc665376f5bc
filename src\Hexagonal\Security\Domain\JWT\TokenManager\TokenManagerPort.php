<?php

declare(strict_types=1);

namespace App\Hexagonal\Security\Domain\JWT\TokenManager;

use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;

interface TokenManagerPort
{
    /**
     * @param string $jwt The JWT
     * @param string|array $key The key, or map of keys.
     * If the algorithm used is asymmetric, this is the public key
     * @param array $allowed_algs List of supported verification algorithms
     * Supported algorithms are 'ES256', 'HS256', 'HS384', 'HS512', 'RS256', 'RS384', and 'RS512'
     *
     * @return object The JWT token payload or false if an error occurs
     */
    public function decode(string $jwt, string $key, array $algs);

    /**
     * Retrieve the kid used to signed the JWT
     * @param string $jwt
     * @return ?string
     */
    public function getKid(string $jwt): ?string;
}
