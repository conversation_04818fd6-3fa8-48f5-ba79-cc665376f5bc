<?php

declare(strict_types=1);

namespace App\Hexagonal\Forecast\Adapters\Database\GoogleAdManager;

use App\Hexagonal\Forecast\Domain\Model\ForecastParams;
use App\Hexagonal\Places\Domain\PlaceRepositoryPort;
use App\Hexagonal\Forecast\Domain\Database\AdManagerDeviceRepositoryPort;
use App\Hexagonal\Forecast\Domain\Database\AdManagerFormatRepositoryPort;
use App\Hexagonal\Forecast\Domain\Database\AdUnitRepositoryPort;
use App\Hexagonal\Forecast\Domain\Database\AdManagerCustomTargetPosRepositoryPort;

class GoogleOptions
{
    private const KEY_ID_CUSTOM_TARGET_DEPARTMENT = 310968;
    private const KEY_ID_CUSTOM_TARGET_CP = 311088;
    private const LINE_PRIORITY = 6;
    private const ROADBLOCKING_ONLY_ONE = 'ONLY_ONE';
    private const ROADBLOCKING_ONE_OR_MORE = 'ONE_OR_MORE';

    private PlaceRepositoryPort $placeRepository;
    private AdManagerDeviceRepositoryPort $adManagerDeviceRepository;
    private AdManagerFormatRepositoryPort $adManagerFormatRepository;
    private AdUnitRepositoryPort $adUnitRepository;
    private AdManagerCustomTargetPosRepositoryPort $customTargetPosRepository;
    private string $startDate;
    private string $endDate;
    private array $formatSizes;
    private array $deviceIds;
    private array $customTargetings;
    private int $linePriority;
    private array $adUnitIds;
    private array $frequencyCap;
    private string $roadBlockingType;

    /**
     * @param PlaceRepositoryPort $placeRepository
     * @param AdManagerDeviceRepositoryPort $adManagerDeviceRepository
     * @param AdManagerFormatRepositoryPort $adManagerFormatRepository
     * @param AdUnitRepositoryPort $adUnitRepository
     * @param AdManagerCustomTargetPosRepositoryPort $customTargetPosRepository
     */
    public function __construct(
        PlaceRepositoryPort $placeRepository,
        AdManagerDeviceRepositoryPort $adManagerDeviceRepository,
        AdManagerFormatRepositoryPort $adManagerFormatRepository,
        AdUnitRepositoryPort $adUnitRepository,
        AdManagerCustomTargetPosRepositoryPort $customTargetPosRepository
    ) {
        $this->placeRepository = $placeRepository;
        $this->adManagerDeviceRepository = $adManagerDeviceRepository;
        $this->adManagerFormatRepository = $adManagerFormatRepository;
        $this->adUnitRepository = $adUnitRepository;
        $this->customTargetPosRepository = $customTargetPosRepository;
    }

    public function setOptions(ForecastParams $forecastParams): void
    {
        $product = $forecastParams->getProduct();

        $pages = json_decode($product->getAdmanagerAdunit(), true);
        $aUnits = [];
        foreach ($forecastParams->getProjectType() as $projectType) {
            $aUnits = array_merge($aUnits, explode(",", $pages[strtolower($projectType)]));
        }
        $this->setAdUnitIds($aUnits);

        $this->setDeviceIds(explode(",", $product->getAdmanagerDevice()));
        $this->setCustomTargetings($forecastParams->getPostalCode(), $product->getAdmanagerCustomTargetPos());
        $this->setFormatSizes(explode(",", $product->getAdmanagerFormatSizes()));
        $this->linePriority = self::LINE_PRIORITY;
        $this->frequencyCap = [3, 1, 'DAY'];
        $this->startDate = $forecastParams->getStartDate();
        $this->endDate = $forecastParams->getEndDate();
        $this->roadBlockingType = ($product->getId() === 2) ? self::ROADBLOCKING_ONLY_ONE : self::ROADBLOCKING_ONE_OR_MORE;
    }

    /**
     * @return string
     */
    public function getStartDate(): string
    {
        return $this->startDate;
    }

    /**
     * @return string
     */
    public function getEndDate(): string
    {
        return $this->endDate;
    }

    /**
     * @return array
     */
    public function getFormatSizes(): array
    {
        return $this->formatSizes;
    }

    /**
     * @return array
     */
    public function getDeviceIds(): array
    {
        return $this->deviceIds;
    }

    /**
     * @return array
     */
    public function getCustomTargetings(): array
    {
        return $this->customTargetings;
    }

    /**
     * @return int
     */
    public function getLinePriority(): int
    {
        return $this->linePriority;
    }

    /**
     * @return array
     */
    public function getAdUnitIds(): array
    {
        return $this->adUnitIds;
    }

    /**
     * @return array
     */
    public function getFrequencyCap(): array
    {
        return $this->frequencyCap;
    }

    /**
     * Get custom Targetings by postal codes
     * @param array $postalCodes
     * @param array $cusTargetIdFromProduct
     */
    private function setCustomTargetings(array $postalCodes, array $cusTargetIdFromProduct): void
    {
        $customTargetings = [];

        $places = $this->placeRepository->getByCodes($postalCodes);
        $aCusTargetPos = $this->getCustomTargetPos($cusTargetIdFromProduct);

        foreach ($aCusTargetPos as $cusTargetPos) {
            $aCustomTargetPos[] = ['id' => $cusTargetPos->getCustomPosId(), 'target' => $cusTargetPos->getAdmanagerId()];
        }

        foreach ($places as $place) {
            $keyIdCustomTarget = (strlen($place->getCode()) === 2) ? self::KEY_ID_CUSTOM_TARGET_DEPARTMENT : self::KEY_ID_CUSTOM_TARGET_CP;
            $aCustomTargetPos[] = ['id' => $keyIdCustomTarget, 'target' => $place->getAdmanagerId()];
            $customTargetings[] = $aCustomTargetPos;
            // remove last postalCode targeting and keep others pos
            array_pop($aCustomTargetPos);
        }

        $this->customTargetings = $customTargetings;
    }

    /**
     * Set all devices from 'admanager_device' by listId
     *
     * @param array $listId
     */
    private function setDeviceIds(array $listId): void
    {
        $this->deviceIds = $this->adManagerDeviceRepository->getByIds($listId);
    }

    /**
     * Expected Creatives
     * Set all formats from 'admanager_format_sizes' by listId
     *
     * @param array $listId
     */
    private function setFormatSizes(array $listId): void
    {
        $this->formatSizes = $this->adManagerFormatRepository->getByIds($listId);
    }

    /**
     * AdUnit Targeting
     * Set all emplacement from 'admanager_adunit' by listId
     *
     * @param array $listId
     */
    private function setAdUnitIds(array $listId): void
    {
        $this->adUnitIds = $this->adUnitRepository->getByIds($listId);
    }

    /**
     * get all custom param from 'admanager_custom_target_pos' by listId
     *
     * @param array $listId
     * @return array
     */
    private function getCustomTargetPos(array $listId): array
    {
        return $this->customTargetPosRepository->getByIds($listId);
    }

    /**
     * Get the value of roadBlockingType
     * @return string
     */
    public function getRoadBlockingType(): string
    {
        return $this->roadBlockingType;
    }
}
