<?php

declare(strict_types=1);

namespace App\Hexagonal\Client\Adapters\Database;

use App\Entity\Client;
use App\Hexagonal\Client\Domain\ClientRepositoryPort;
use App\Hexagonal\Client\Domain\CreateClientPort;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;

final class CreateClientCSVAdapter implements CreateClientPort
{
    private ClientRepositoryPort $clientRepository;
    private ParameterBagInterface $params;

    private const FILENAME = 'boost360.csv';
    private const BATCH_SIZE = 5000;

    public function __construct(ClientRepositoryPort $clientRepository, ParameterBagInterface $params)
    {
        $this->clientRepository = $clientRepository;
        $this->params = $params;
    }

    /**
     * @inheritDoc
     */
    public function bulkSave(): void
    {
        $pathToCSV = $this->params->get('tmpDir') . self::FILENAME;

        $batchCount = 0;
        $clients = [];

        $pathToCSV = $this->params->get('tmpDir') . self::FILENAME;
        if (($handle = fopen($pathToCSV, 'r')) !== FALSE) {
            // skip headers
            fgetcsv($handle);
            while (($csvClient = fgetcsv($handle, 0, ',')) !== FALSE) {
                $clients[$csvClient[0]] = $this->toClient($csvClient);
                $batchCount++;

                if ($batchCount % self::BATCH_SIZE === 0) {
                    $this->clientRepository->bulkSave($clients);
                    $clients = [];
                }
            }

            $this->clientRepository->bulkSave($clients);
            fclose($handle);
        }
    }

    /**
     * Convert array client to Client
     * @param array $client
     * @return Client
     */
    private function toClient(array $client): Client
    {
        $intensity = [
            ['prints' => $client[2] === '' ? NULL : intval($client[2]), 'impressions' => intval($client[3]), 'budget' => intval
            ($client[1])],
            ['prints' => $client[6] === '' ? NULL : intval($client[6]), 'impressions' => intval($client[7]), 'budget' => intval
            ($client[5])],
            ['prints' => $client[10] === '' ? NULL : intval($client[10]), 'impressions' => intval($client[11]), 'budget' => intval
            ($client[9])]
        ];

        return new Client(null, $client[0], $intensity);
    }
}
