<?php

declare(strict_types=1);

namespace App\Controller;

use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Nesk\Puphpeteer\Puppeteer;
use App\Hexagonal\Count\Domain\Database\CountRepositoryPort;
use App\Entity\Count;

class PdfController extends AbstractController
{
    private Puppeteer $puppeteer;
    private CountRepositoryPort $countRepository;
    private string $baseURL;


    /**
     * PdfController constructor.
     * @param Puppeteer $puppeteer
     * @param CountRepositoryPort $countRepository
     * @param string $baseURL
     */
    public function __construct(Puppeteer $puppeteer, CountRepositoryPort $countRepository, string $baseURL)
    {
        $this->puppeteer = $puppeteer;
        $this->countRepository = $countRepository;
        $this->baseURL = $baseURL;
    }

    /**
     * Download the count report by id
     * @Route("/bff/api/pdf/counts/print/{id}", name="app_api_pdf_counts_print")
     *
     * @param int $id count id
     * @param Request $request
     * @return Response
     */
    public function print(int $id, Request $request): Response
    {
        try {
            $count = $this->getCount($id);
        } catch (NotFoundHttpException $e) {
            return new JsonResponse($e->getMessage(), 404);
        }

        $pathToFile = $this->getParameter('tmpDir') . 'MAF-' . $id . '.pdf';
        $this->generatePdf($pathToFile, $count, $request);

        $response = new BinaryFileResponse($pathToFile);
        $response->setContentDisposition(ResponseHeaderBag::DISPOSITION_ATTACHMENT);

        return $response->deleteFileAfterSend(true);
    }

    /**
     * Check count exists by id
     * @param int $id
     * @return Count|null
     */
    private function getCount(int $id): ?Count
    {
        $count = $this->countRepository->get($id);
        if (!$count) {
            throw $this->createNotFoundException('The count does not exist');
        }

        return $count;
    }

    /**
     * Generate pdf with puppeteer
     * @param string $pathToFile
     * @param Count $count
     * @param Request $request
     */
    private function generatePdf(string $pathToFile, Count $count, Request $request): void
    {
        $browser = $this->puppeteer->launch([
            'args' =>
                [
                    // this is required for dockerized puppeteer
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                ],
            'ignoreHTTPSErrors' => true
        ]);

        $page = $browser->newPage();
        $page->setExtraHTTPHeaders([
            'Authorization' => $request->headers->get('Authorization')
        ]);

        $page->goto(
            'https://' . $this->baseURL . '/api/pdf/counts/' . $count->getId(),
            [
                'waitUntil' => 'networkidle0',
            ]
        );

        $page->emulateMediaType('screen');
        $page->pdf([
            'path' => $pathToFile,
            'width' => '13.5in',
            'height' => '24in',
            'printBackground' => true
        ]);

        $browser->close();
    }

    /**
     * Download the count report by id
     * @Route("/bff/api/pdf/counts/{id}", name="app_api_pdf_counts")
     *
     * @param int $id
     * @return Response
     */
    public function getPdfPage(int $id): Response
    {
        $count = $this->getCount($id);
        $templatePath = 'MasterDisplay';

        // different template depending on productId
        if ($count->getProductId() === 2) {
            $templatePath = 'Expert360';
        }

        return $this->render('proposalClient/' . $templatePath . '/summary.html.twig',
            [
                'count' => $count
            ]
        );
    }
}