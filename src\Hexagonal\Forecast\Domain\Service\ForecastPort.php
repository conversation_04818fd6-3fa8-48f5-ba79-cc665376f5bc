<?php

declare(strict_types=1);

namespace App\Hexagonal\Forecast\Domain\Service;

interface ForecastPort
{
    /**
     * @param int $productId
     * @param array $postalCode
     * @param array $broadcastMedium
     * @param array $projectType
     * @param string $startDate
     * @param string $endDate
     * @return array Forecast[]
     */
    public function get(int $productId,
                        array $postalCode,
                        array $broadcastMedium,
                        array $projectType,
                        string $startDate,
                        string $endDate): array;
}