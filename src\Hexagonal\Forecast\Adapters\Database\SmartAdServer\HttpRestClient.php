<?php

declare(strict_types=1);

namespace App\Hexagonal\Forecast\Adapters\Database\SmartAdServer;

use Psr\Log\LoggerInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Contracts\HttpClient\Exception\HttpExceptionInterface;

class HttpRestClient
{
    private HttpClientInterface $client;
    private LoggerInterface $logger;
    private array $auth;

    public function __construct(HttpClientInterface $client, LoggerInterface $logger)
    {
        $this->client = $client;
        $this->logger = $logger;
    }

    /**
     * Set auth array for api usage
     *
     * @param array $auth
     */
    public function setAuth(array $auth)
    {
        $this->auth = $auth;
    }

    /**
     * Get auth basic/bearer (must initialize setAuth first)
     *
     * @return mixed
     */
    private function getAuth()
    {
        // auth_basic [user, password]
        if ($this->auth['type'] === 'auth_basic') {
            return [
                $this->auth['user'],
                $this->auth['password'],
            ];
        }

        // auth_bearer token
        return $this->auth['token'];
    }

    /**
     * Generic method for http request.
     *
     * @param string $method
     * @param string $uri
     * @param array $query
     *
     * @return array
     */
    public function dispatchRequest(string $method, string $uri, array $query): array
    {
        try {
            $authType = $this->auth['type'];

            if ($method === 'GET') {
                $response = $this->client->request($method, $uri, [
                    'query' => $query,
                    $authType => $this->getAuth(),
                ]);
            } else {
                $response = $this->client->request($method, $uri, [
                    'json' => $query,
                    $authType => $this->getAuth(),
                ]);
            }

            return ['headers' => $response->getHeaders(), 'content' => $response->getContent() ? $response->toArray() : ''];
        } catch (HttpExceptionInterface $e) {
            $this->logger->error($e->getMessage());
        }
    }

    /**
     * GET method.
     *
     * @param string $uri
     * @param array $query
     *
     * @return array
     */
    public function get(string $uri, array $query = []): array
    {
        return $this->dispatchRequest('GET', $uri, $query);
    }

    /**
     * POST method.
     *
     * @param string $uri
     * @param array $query
     *
     * @return array
     */
    public function post(string $uri, array $query): array
    {
        return $this->dispatchRequest('POST', $uri, $query);
    }

    /**
     * PUT method.
     *
     * @param string $uri
     * @param array $query
     *
     * @return array
     */
    public function put(string $uri, array $query): array
    {
        return $this->dispatchRequest('PUT', $uri, $query);
    }

    /**
     * PATCH method.
     *
     * @param string $uri
     * @param array $query
     *
     * @return array
     */
    public function patch(string $uri, array $query): array
    {
        return $this->dispatchRequest('PATCH', $uri, $query);
    }
}
