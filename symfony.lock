{"api-platform/api-pack": {"version": "v1.3.0"}, "api-platform/core": {"version": "2.5", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "2.5", "ref": "a93061567140e386f107be75340ac2aee3f86cbf"}, "files": ["config/packages/api_platform.yaml", "config/routes/api_platform.yaml", "src/Entity/.gitignore"]}, "clue/socket-raw": {"version": "v1.5.0"}, "codercat/jwk-to-pem": {"version": "1.0"}, "composer/package-versions-deprecated": {"version": "*********"}, "doctrine/annotations": {"version": "1.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.0", "ref": "a2759dd6123694c8d901d0ec80006e044c2e6457"}, "files": ["config/routes/annotations.yaml"]}, "doctrine/cache": {"version": "1.10.2"}, "doctrine/collections": {"version": "1.6.7"}, "doctrine/common": {"version": "3.1.1"}, "doctrine/dbal": {"version": "2.12.1"}, "doctrine/doctrine-bundle": {"version": "2.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "2.0", "ref": "40631978d2c4adc9b11220b13eba539b727c36a8"}, "files": ["config/packages/doctrine.yaml", "config/packages/prod/doctrine.yaml", "src/Entity/.gitignore", "src/Repository/.gitignore"]}, "doctrine/doctrine-migrations-bundle": {"version": "2.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "2.2", "ref": "baaa439e3e3179e69e3da84b671f0a3e4a2f56ad"}, "files": ["./config/packages/doctrine_migrations.yaml", "./migrations/.gitignore"]}, "doctrine/event-manager": {"version": "1.1.1"}, "doctrine/inflector": {"version": "2.0.3"}, "doctrine/instantiator": {"version": "1.4.0"}, "doctrine/lexer": {"version": "1.2.1"}, "doctrine/migrations": {"version": "3.0.1"}, "doctrine/orm": {"version": "2.8.2"}, "doctrine/persistence": {"version": "2.1.0"}, "doctrine/reflection": {"version": "1.2.1"}, "doctrine/sql-formatter": {"version": "1.1.1"}, "fig/link-util": {"version": "1.1.2"}, "firebase/php-jwt": {"version": "v5.2.1"}, "friendsofphp/proxy-manager-lts": {"version": "v1.0.3"}, "google/auth": {"version": "v1.15.0"}, "googleads/googleads-php-lib": {"version": "48.0.0"}, "guzzlehttp/guzzle": {"version": "6.5.5"}, "guzzlehttp/promises": {"version": "1.4.0"}, "guzzlehttp/psr7": {"version": "1.7.0"}, "laminas/laminas-code": {"version": "3.4.1"}, "laminas/laminas-eventmanager": {"version": "3.2.1"}, "laminas/laminas-zendframework-bridge": {"version": "1.0.4"}, "monolog/monolog": {"version": "2.2.0"}, "nelmio/cors-bundle": {"version": "1.5", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.5", "ref": "6bea22e6c564fba3a1391615cada1437d0bde39c"}, "files": ["config/packages/nelmio_cors.yaml"]}, "nesk/puphpeteer": {"version": "2.0.0"}, "nesk/rialto": {"version": "1.4.0"}, "nikic/php-parser": {"version": "v4.10.4"}, "ocramius/proxy-manager": {"version": "2.9.0"}, "phpdocumentor/reflection-common": {"version": "2.2.0"}, "phpdocumentor/reflection-docblock": {"version": "5.2.2"}, "phpdocumentor/type-resolver": {"version": "1.4.0"}, "phpseclib/phpseclib": {"version": "2.0.30"}, "psr/cache": {"version": "1.0.1"}, "psr/container": {"version": "1.0.0"}, "psr/event-dispatcher": {"version": "1.0.0"}, "psr/http-client": {"version": "1.0.1"}, "psr/http-message": {"version": "1.0.1"}, "psr/link": {"version": "1.0.0"}, "psr/log": {"version": "1.1.3"}, "ralouphie/getallheaders": {"version": "3.0.3"}, "sensio/framework-extra-bundle": {"version": "5.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "5.2", "ref": "fb7e19da7f013d0d422fa9bce16f5c510e27609b"}, "files": ["config/packages/sensio_framework_extra.yaml"]}, "symfony/asset": {"version": "v5.1.11"}, "symfony/cache": {"version": "v5.1.11"}, "symfony/cache-contracts": {"version": "v1.1.10"}, "symfony/config": {"version": "v5.1.11"}, "symfony/console": {"version": "5.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "5.1", "ref": "c6d02bdfba9da13c22157520e32a602dbee8a75c"}, "files": ["bin/console"]}, "symfony/dependency-injection": {"version": "v5.1.11"}, "symfony/deprecation-contracts": {"version": "v2.2.0"}, "symfony/doctrine-bridge": {"version": "v5.1.11"}, "symfony/dotenv": {"version": "v5.1.11"}, "symfony/error-handler": {"version": "v5.1.11"}, "symfony/event-dispatcher": {"version": "v5.1.11"}, "symfony/event-dispatcher-contracts": {"version": "v2.2.0"}, "symfony/expression-language": {"version": "v5.1.11"}, "symfony/filesystem": {"version": "v5.1.11"}, "symfony/finder": {"version": "v5.1.11"}, "symfony/flex": {"version": "1.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.0", "ref": "c0eeb50665f0f77226616b6038a9b06c03752d8e"}, "files": [".env"]}, "symfony/framework-bundle": {"version": "5.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "5.1", "ref": "5f0d0fd82ffa3580fe0ce8e3b2d18506ebf37a0e"}, "files": ["config/packages/cache.yaml", "config/packages/framework.yaml", "config/packages/test/framework.yaml", "config/preload.php", "config/routes/dev/framework.yaml", "config/services.yaml", "public/index.php", "src/Controller/.gitignore", "src/Kernel.php"]}, "symfony/http-client": {"version": "v5.1.7"}, "symfony/http-client-contracts": {"version": "v2.2.0"}, "symfony/http-foundation": {"version": "v5.1.11"}, "symfony/http-kernel": {"version": "v5.1.11"}, "symfony/intl": {"version": "v5.2.4"}, "symfony/maker-bundle": {"version": "1.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.0", "ref": "fadbfe33303a76e25cb63401050439aa9b1a9c7f"}}, "symfony/mime": {"version": "v5.2.6"}, "symfony/monolog-bridge": {"version": "v5.1.11"}, "symfony/monolog-bundle": {"version": "3.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.3", "ref": "d7249f7d560f6736115eee1851d02a65826f0a56"}, "files": ["config/packages/dev/monolog.yaml", "config/packages/prod/deprecations.yaml", "config/packages/prod/monolog.yaml", "config/packages/test/monolog.yaml"]}, "symfony/orm-pack": {"version": "v2.0.0"}, "symfony/phpunit-bridge": {"version": "5.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "5.1", "ref": "bf16921ef8309a81d9f046e9b6369c46bcbd031f"}, "files": [".env.test", "bin/phpunit", "phpunit.xml.dist", "tests/bootstrap.php"]}, "symfony/polyfill-intl-grapheme": {"version": "v1.22.1"}, "symfony/polyfill-intl-icu": {"version": "v1.22.1"}, "symfony/polyfill-intl-idn": {"version": "v1.22.1"}, "symfony/polyfill-intl-normalizer": {"version": "v1.22.1"}, "symfony/polyfill-mbstring": {"version": "v1.22.1"}, "symfony/polyfill-php73": {"version": "v1.22.1"}, "symfony/polyfill-php80": {"version": "v1.22.1"}, "symfony/process": {"version": "v5.2.4"}, "symfony/profiler-pack": {"version": "v1.0.5"}, "symfony/property-access": {"version": "v5.1.11"}, "symfony/property-info": {"version": "v5.1.11"}, "symfony/proxy-manager-bridge": {"version": "v5.1.11"}, "symfony/routing": {"version": "5.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "5.1", "ref": "b4f3e7c95e38b606eef467e8a42a8408fc460c43"}, "files": ["config/packages/prod/routing.yaml", "config/packages/routing.yaml", "config/routes.yaml"]}, "symfony/security-bundle": {"version": "5.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "5.1", "ref": "0a4bae19389d3b9cba1ca0102e3b2bccea724603"}, "files": ["config/packages/security.yaml"]}, "symfony/security-core": {"version": "v5.1.11"}, "symfony/security-csrf": {"version": "v5.1.11"}, "symfony/security-guard": {"version": "v5.1.11"}, "symfony/security-http": {"version": "v5.1.11"}, "symfony/serializer": {"version": "v5.1.11"}, "symfony/serializer-pack": {"version": "v1.0.3"}, "symfony/service-contracts": {"version": "v2.2.0"}, "symfony/stopwatch": {"version": "v5.1.3"}, "symfony/string": {"version": "v5.1.11"}, "symfony/translation-contracts": {"version": "v1.1.10"}, "symfony/twig-bridge": {"version": "v5.1.11"}, "symfony/twig-bundle": {"version": "5.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "5.0", "ref": "fab9149bbaa4d5eca054ed93f9e1b66cc500895d"}, "files": ["config/packages/test/twig.yaml", "config/packages/twig.yaml", "templates/base.html.twig"]}, "symfony/validator": {"version": "4.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.3", "ref": "d902da3e4952f18d3bf05aab29512eb61cabd869"}, "files": ["config/packages/test/validator.yaml", "config/packages/validator.yaml"]}, "symfony/var-dumper": {"version": "v5.1.11"}, "symfony/var-exporter": {"version": "v5.1.11"}, "symfony/web-link": {"version": "v5.1.11"}, "symfony/web-profiler-bundle": {"version": "3.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.3", "ref": "6bdfa1a95f6b2e677ab985cd1af2eae35d62e0f6"}, "files": ["config/packages/dev/web_profiler.yaml", "config/packages/test/web_profiler.yaml", "config/routes/dev/web_profiler.yaml"]}, "symfony/webpack-encore-bundle": {"version": "1.9", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.9", "ref": "9399a0bfc6ee7a0c019f952bca46d6a6045dd451"}, "files": ["assets/app.js", "assets/bootstrap.js", "assets/controllers.json", "assets/controllers/hello_controller.js", "assets/styles/app.css", "config/packages/assets.yaml", "config/packages/prod/webpack_encore.yaml", "config/packages/test/webpack_encore.yaml", "config/packages/webpack_encore.yaml", "package.json", "webpack.config.js"]}, "symfony/yaml": {"version": "v5.1.11"}, "twig/extra-bundle": {"version": "v3.3.0"}, "twig/intl-extra": {"version": "v3.3.0"}, "twig/twig": {"version": "v3.3.0"}, "vierbergenlars/php-semver": {"version": "3.0.2"}, "webimpress/safe-writer": {"version": "2.0.1"}, "webmozart/assert": {"version": "1.9.1"}, "willdurand/negotiation": {"version": "3.0.0"}}