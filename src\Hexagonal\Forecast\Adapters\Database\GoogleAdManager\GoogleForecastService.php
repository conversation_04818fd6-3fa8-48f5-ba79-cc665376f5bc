<?php

declare(strict_types=1);

namespace App\Hexagonal\Forecast\Adapters\Database\GoogleAdManager;

use Psr\Log\LoggerInterface;
use Symfony\Component\Config\Definition\Exception\Exception;
use Google\AdsApi\AdManager\AdManagerSession;
use Google\AdsApi\AdManager\v202102\AvailabilityForecast;
use Google\AdsApi\AdManager\v202102\LineItem;
use Google\AdsApi\AdManager\v202102\ServiceFactory;
use Google\AdsApi\AdManager\v202102\ProspectiveLineItem;
use Google\AdsApi\AdManager\v202102\AvailabilityForecastOptions;
use Google\AdsApi\AdManager\v202102\ApiException;

class GoogleForecastService
{
    private AdManagerSession $session;
    private ServiceFactory $serviceFactory;
    private ProspectiveLineItem $prospectiveLineItem;
    private AvailabilityForecastOptions $availabilityForecastOptions;
    private LoggerInterface $logger;

    public function __construct(SessionService $session,
                                ServiceFactory $serviceFactory,
                                ProspectiveLineItem $prospectiveLineItem,
                                AvailabilityForecastOptions $availabilityForecastOptions,
                                LoggerInterface $logger)
    {
        $this->session = $session->getAdManagerSession();
        $this->serviceFactory = $serviceFactory;
        $this->prospectiveLineItem = $prospectiveLineItem;
        $this->availabilityForecastOptions = $availabilityForecastOptions;
        $this->logger = $logger;
    }

    /**
     * @param LineItem $lineItem
     * @return array
     * @throws ApiException
     */
    public function getForecast(LineItem $lineItem): array
    {
        try {
            $this->prospectiveLineItem->setLineItem($lineItem);
            $this->availabilityForecastOptions->setIncludeTargetingCriteriaBreakdown(true);
            $forecastService = $this->serviceFactory->createForecastService($this->session);

            $forecast = $forecastService->getAvailabilityForecast(
                $this->prospectiveLineItem,
                $this->availabilityForecastOptions
            );

            return $this->getForecastResult($forecast);
        } catch (Exception $e) {
            $this->logger->error($e->getMessage());
        }
    }

    /**
     * Retrieve the CUSTOM_CRITERIA breakdowns (postalCode & dpt)
     * @param array TargetingCriteriaBreakdown[] $targetingCriteriaBreakdowns
     * @return array
     */
    private function getCustomCriteriaBreakdowns(array $targetingCriteriaBreakdowns): array
    {
        return array_filter($targetingCriteriaBreakdowns,
            function ($targetingCriteriaBreakdown) {
                return $targetingCriteriaBreakdown->getTargetingDimension() === 'CUSTOM_CRITERIA';
            });
    }

    /**
     * Retrieve the forecast result, global and breakdowns
     * @param AvailabilityForecast $forecast
     * @return array
     */
    private function getForecastResult(AvailabilityForecast $forecast): array
    {
        $forecastResults = [];

        // postalCodes breakdowns
        $customCriteriaBreakdowns = $this->getCustomCriteriaBreakdowns($forecast->getTargetingCriteriaBreakdowns());
        foreach ($customCriteriaBreakdowns as $customCriteriaBreakdown) {
            $matchedUnits = $customCriteriaBreakdown->getMatchedUnits();
            $availableUnits = $customCriteriaBreakdown->getAvailableUnits();
            // "AND(pos=banner-haute-habillage,cp=92340)" ou "AND(OR(pos=banner-haute-habillage,pos=native2,pos=native3,pos=native5,pos=native6,pos=native7,pos=pave-haut,pos=pave-bas),cp=93150)"
            // "AND(pos=banner-haute-habillage,dpt=92)" ou "AND(OR(pos=banner-haute-habillage,pos=native2,pos=native3,pos=native5,pos=native6,pos=native7,pos=pave-haut,pos=pave-bas),cp=93)"
            $criteriaName = $customCriteriaBreakdown->getTargetingCriteriaName();
            $nbEqual = substr_count($criteriaName, '='); 
            $postalCode = substr(explode('=', $criteriaName)[$nbEqual], 0, -1);

            $forecastResults[$postalCode] = [
                'matched' => $matchedUnits,
                'available' => $availableUnits,
                'sold' => $matchedUnits - $availableUnits,
            ];
        }

        return $forecastResults;
    }
}
