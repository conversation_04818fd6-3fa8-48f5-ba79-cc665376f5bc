<?php

declare(strict_types=1);

namespace App\Hexagonal\Forecast\Adapters;

use App\Hexagonal\Places\Domain\PlaceRepositoryPort;
use App\Hexagonal\Product\Domain\Database\ProductRepositoryPort;

class ForecastApiRequestValidator
{
    private string $errorMessage;
    private ProductRepositoryPort $productRepository;
    private PlaceRepositoryPort $placeRepository;

    private const MIN_LENGTH_POSTAL_CODE = 2;
    private const MAX_LENGTH_POSTAL_CODE = 5;
    private const MAX_POSTAL_CODE = 15;
    private const PROJECT_TYPE = ['achat', 'location'];
    private const BROADCAST_MEDIUM = ['seloger', 'logicimmo'];

    /**
     * @param ProductRepositoryPort $productRepository
     */
    public function __construct(ProductRepositoryPort $productRepository, PlaceRepositoryPort $placeRepository)
    {
        $this->productRepository = $productRepository;
        $this->placeRepository = $placeRepository;
    }

    /**
     * Check for errors in request parameters
     * @param ForecastApiRequest $forecastApiRequest
     * @return bool
     */
    public function validate(ForecastApiRequest $forecastApiRequest): bool
    {
        $postalCodes = $forecastApiRequest->getPostalCode();
        if (!$postalCodes) {
            $this->errorMessage = 'Postal code is mandatory.';
            return false;
        }

        if (count($postalCodes) > self::MAX_POSTAL_CODE) {
            $this->errorMessage = 'Maximum postal codes is ' . self::MAX_POSTAL_CODE . '.';
            return false;
        }

        $dbPostalCodesCount = count($this->placeRepository->getByCodes($postalCodes));
        if(count($postalCodes) !== $dbPostalCodesCount){
            $this->errorMessage = 'Unknown postal code(s).';
            return false;
        }

        foreach ($postalCodes as $postalCode) {
            if ((strlen($postalCode) !== self::MIN_LENGTH_POSTAL_CODE &&
                    strlen($postalCode) !== self::MAX_LENGTH_POSTAL_CODE)) {
                $this->errorMessage = 'Postal code must be 2 or 5 digits.';
                return false;
            }
        }

        $productId = $forecastApiRequest->getProductId();
        if (!$productId) {
            $this->errorMessage = 'Product id is mandatory.';
            return false;
        }

        if (!$this->isProductIdValid($productId)) {
            $this->errorMessage = 'Product id is unknown.';
            return false;
        }

        // temporary disable
//        // default startDate : now + 5 min
//        $startDate = $query->has('startDate') && ($sd = \DateTime::createFromFormat('Y-m-d',
//            $requestParameters['startDate'])) ?
//            $sd->add(new \DateInterval('PT5M'))->format('Y-m-d H:i:s') : date('Y-m-d H:i:s', strtotime('+5 min'));
//        // default endDate : +30d
//        $endDate = $query->has('endDate') && ($ed = \DateTime::createFromFormat('Y-m-d',
//            $requestParameters['endDate'])) ?
//            $ed->add(new \DateInterval('PT5M'))->format('Y-m-d H:i:s') : date('Y-m-d H:i:s', strtotime('+30 days'));
//
//        if ($startDate > $endDate) {
//            $this->errorMessage = 'The endDate must be greater than the startDate.';
//            return false;
//        }

        $broadcastMediums = $forecastApiRequest->getBroadcastMedium();
        if (!$broadcastMediums) {
            $this->errorMessage = 'Broadcast medium is mandatory.';
            return false;
        }

        if (!in_array(self::BROADCAST_MEDIUM[0], $broadcastMediums)) {
            $this->errorMessage = 'Broadcast medium must be at least seloger.';
            return false;
        }
        foreach ($broadcastMediums as $broadcastMedium) {
            if (!in_array($broadcastMedium, self::BROADCAST_MEDIUM)) {
                $this->errorMessage = 'Broadcast medium must be seloger or logicimmo.';
                return false;
            }
        }

        $projectTypes = $forecastApiRequest->getProjectType();
        if (!$projectTypes) {
            $this->errorMessage = 'Project type is mandatory.';
            return false;
        }

        foreach ($projectTypes as $projectType) {
            if (!in_array($projectType, self::PROJECT_TYPE)) {
                $this->errorMessage = 'Project type must be achat or location or achat,location.';
                return false;
            }
        }

        return true;
    }

    /**
     * @return string
     */
    public function getErrors(): string
    {
        return $this->errorMessage;
    }

    /**
     * @param string $errorMessage
     */
    public function setErrors(string $errorMessage): void
    {
        $this->errorMessage = $errorMessage;
    }

    /**
     * Check if product exists
     *
     * @param int $productId
     * @return bool
     */
    private function isProductIdValid(int $productId): bool
    {
        $product = $this->productRepository->get($productId);

        return $product === null ? false : true;
    }
}