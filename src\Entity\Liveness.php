<?php

namespace App\Entity;

use ApiPlatform\Core\Annotation\ApiResource;
use ApiPlatform\Core\Annotation\ApiProperty;
use ApiPlatform\Core\Action\NotFoundAction;

/**
 * Liveness endpoint used by EKS pods
 *
 * @ApiResource(
 *     formats={"json"},
 *     collectionOperations={
 *         "get"={"path"="/healthcheck/liveness", "status"=200}
 *     },
 *     itemOperations={
 *         "get"={
 *             "path"="/healthcheck/liveness/{id}",
 *             "controller"=NotFoundAction::class,
 *             "read"=false,
 *             "output"=false,
 *         },
 *     },
 * )
 */
class Liveness
{
    /**
     * @ApiProperty(identifier=true)
     */
    private int $id;
}
