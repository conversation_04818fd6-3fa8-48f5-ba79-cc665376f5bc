<?php

namespace App\Entity;

use ApiPlatform\Core\Annotation\ApiResource;
use ApiPlatform\Core\Annotation\ApiProperty;
use ApiPlatform\Core\Action\NotFoundAction;

/**
 * Readiness endpoint used by EKS pods
 *
 * @ApiResource(
 *     formats={"json"},
 *     collectionOperations={
 *         "get"={"path"="/healthcheck/readiness", "status"=200}
 *     },
 *     itemOperations={
 *         "get"={
 *             "path"="/healthcheck/readiness/{id}",
 *             "controller"=NotFoundAction::class,
 *             "read"=false,
 *             "output"=false,
 *         },
 *     },
 * )
 */
class Readiness
{
    /**
     * @ApiProperty(identifier=true)
     */
    private int $id;
}
