# This file is the entry point to configure your own services.
# Files in the packages/ subdirectory configure your dependencies.

# Put parameters here that don't need to change on each machine where the app is deployed
# https://symfony.com/doc/current/best_practices/configuration.html#application-related-configuration
imports:

parameters:
    tmpDir: '/var/www/var/tmp/'

services:
    # default configuration for services in *this* file
    _defaults:
        autowire: true      # Automatically injects dependencies in your services.
        autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.

    # bind AdManager configPath to services
        bind:
            $adManagerConfigPath: '%env(resolve:ADMANAGER_INI)%'
            $baseURL: '%env(resolve:BASE_URL)%'

    # makes classes in src/ available to be used as services
    # this creates a service per class whose id is the fully-qualified class name
    App\:
        resource: '../src/*'
        exclude: '../src/{DependencyInjection,Entity,Migrations,Tests,Kernel.php}'

    # controllers are imported separately to make sure services can be injected
    # as action arguments even if you don't extend any base controller class
    App\Controller\:
        resource: '../src/Controller'
        tags: ['controller.service_arguments']

    # add more service definitions when explicit configuration is needed
    # please note that last definitions always *replace* previous ones

    App\OpenApi\SwaggerDecorator:
        decorates: 'api_platform.openapi.factory'
        autoconfigure: false

    ApiPlatform\Core\OpenApi\Model\Paths:

    Nesk\Puphpeteer\Puppeteer:

    'Google\AdsApi\AdManager\v202102\ServiceFactory':
    'Google\AdsApi\AdManager\v202102\ProspectiveLineItem':
    'Google\AdsApi\AdManager\v202102\AvailabilityForecastOptions':
    'Google\AdsApi\AdManager\v202102\LineItem':

    App\Hexagonal\Forecast\Adapters\Database\SmartAdServer\Auth:
        arguments:
            $smartApiUser: '%env(resolve:SMART_USER)%'
            $smartApiPassword: '%env(resolve:SMART_PASSWORD)%'

    App\DataPersister\UserDataPersister:
        tags: [ 'api_platform.data_persister' ]
    
    App\DataPersister\CountDataPersister:
        tags: [ 'api_platform.data_persister' ]    

    # Hexa archi wiring

    # Adapter alias
    App\Hexagonal\User\Adapters\Database\UserRepositoryPSQLAdapter: ~
    App\Hexagonal\User\Adapters\Database\UserRepositoryInMemoryAdapter: ~
    # the ``App\Hexagonal\User\Adapters\Database\UserRepositoryPSQLAdapter`` service will be
    # injected when an ``App\Hexagonal\User\Domain\Database\UserRepositoryPort``
    # type-hint for a ``$userRepository`` argument is detected.
    App\Hexagonal\User\Domain\Database\UserRepositoryPort $userRepository: '@App\Hexagonal\User\Adapters\Database\UserRepositoryPSQLAdapter'

    # Forecast wiring
    App\Hexagonal\Forecast\Adapters\Database\ForecastRepositoryApiAdapter: ~
    App\Hexagonal\Forecast\Domain\Database\ForecastRepositoryPort $forecastRepository: '@App\Hexagonal\Forecast\Adapters\Database\ForecastRepositoryApiAdapter'
    App\Hexagonal\Forecast\Domain\Database\ForecastRepositoryPort $googleForecastRepository: '@App\Hexagonal\Forecast\Adapters\Database\ForecastRepositoryGoogleAdapter'
    App\Hexagonal\Forecast\Domain\Database\ForecastRepositoryPort $smartForecastRepository: '@App\Hexagonal\Forecast\Adapters\Database\ForecastRepositorySmartAdapter'

    # JWT Security
    App\Hexagonal\Security\Adapters\JWT\TokenExtractor\AuthorizationHeaderTokenExtractor:
        arguments:
            $prefix: 'Bearer'
            $name: 'Authorization'

    App\Hexagonal\Security\Adapters\PublicKeyManager\PublicKeyManager:
        arguments:
            $keyUrl: '%env(resolve:KEYCLOAK_CERTS_URL)%'
            $pathToKey: '%env(resolve:PUBLIC_DIR)%/keycloak.json'

    'CoderCat\JWKToPEM\JWKConverter':
