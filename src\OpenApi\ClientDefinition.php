<?php

declare(strict_types=1);

namespace App\OpenApi;

use ApiPlatform\Core\OpenApi\Model\Operation;
use ApiPlatform\Core\OpenApi\Model\Parameter;
use ApiPlatform\Core\OpenApi\Model\Response;

class ClientDefinition
{
    public static function getGetParameters(): array
    {
        return [
            new Parameter('clientId', 'query', 'client id (RC-XXXXXX)', true, false, false, ['type' => 'string']),
        ];
    }

    public static function getGetItemOperation(): Operation
    {
        $responses = [
            '200' => new Response('Client resource'),
            '404' => new Response('Resource not found'),
        ];

        $parameters = [
            new Parameter('clientId', 'path', 'Resource identifier', true, false, false, ['type' => 'string']),
            new Parameter('projectType', 'query', 'Project type(s)', true, false, false, ['type' => 'string']),
            new Parameter('broadcastMedium', 'query', 'Broadcast medium(s)', true, false, false, ['type' => 'string']),
            new Parameter('postalCode', 'query', 'Postal code(s)', true, false, false, ['type' => 'string']),
            new Parameter('productId', 'query', 'Product id', true, false, false, ['type' => 'string']),
        ];

        return new Operation('getClientItem', ['Client'], $responses, 'Retrieves a Client resource.', 'Retrieves a Client resource.',
            null, $parameters);
    }
}
