<?php

namespace App\Tests\Hexagonal\User\Adapters;

use PHPUnit\Framework\TestCase;
use App\Entity\User;
use App\Hexagonal\User\Adapters\AuthenticateUser;
use App\Hexagonal\User\Domain\Database\UserRepositoryPort;

class AuthenticateUserTest extends TestCase
{
    public function testProcess()
    {
        // arrange
        $userNoId = new User(null, '<EMAIL>', 'firstNameUser4', 'nameUser4');
        $user = new User(4, '<EMAIL>', 'firstNameUser4', 'nameUser4');
        $expected = '<EMAIL>';

        $userRepository = $this->createMock(UserRepositoryPort::class);

        $userRepository
            ->expects($this->any())
            ->method('getByEmail')
            ->with($user->getEmail())
            ->willReturnOnConsecutiveCalls($this->returnValue(null), $this->returnValue($user));

        $userRepository
            ->expects($this->any())
            ->method('create')
            ->with($userNoId)
            ->will($this->returnValue($user));

        $updatedUser = new User(4, '<EMAIL>', 'firstNameUser4', 'nameUser4');

        $userRepository
            ->expects($this->any())
            ->method('update')
            ->will($this->returnValue($updatedUser));

        $authenticateUser = new AuthenticateUser($userRepository);

        // act
        $processed = $authenticateUser->process($userNoId);

        // assert
        $this->assertEquals($expected, $processed->getEmail());
    }
}
