<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\ZipCity;
use Doctrine\Persistence\ManagerRegistry;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;

/**
 * This class is not used as a standalone repository
 *
 * Class BaseDepartmentCityRepository
 * @package App\Repository
 */
class BaseDepartmentCityRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry, string $entityClass = '')
    {
        parent::__construct($registry, $entityClass);
    }

    /**
     * SELECT
     * WHERE name or code
     * LIKE 'XX%'
     *
     * @param string $searchQuery search term
     *
     * @return array
     */
    public function findByNameOrCode(string $searchQuery): array
    {
        $qb = $this->createQueryBuilder('d');

        $searchQuery = $this->strStripSpecialCharacters($searchQuery);

        return $qb
            ->where($qb->expr()->like('d.nameLight', ':searchQuery'))
            ->orWhere($qb->expr()->like('d.code', ':searchQuery'))
            ->setParameter('searchQuery', $searchQuery . '%')
            ->orderBy('d.name')
            ->orderBy('d.code')
            ->setMaxResults(50)
            ->getQuery()
            ->getResult();
    }

    /**
     * Return an array of city by codes
     * @param array $codes
     * @return array
     */
    public function getByCodes(array $codes): array
    {
        $results = [];
        $qb = $this->createQueryBuilder('d');

        $dbResults = $qb
            ->select('d.code', 'd.admanagerId', 'd.name')
            ->where($qb->expr()->in('d.code', $codes))
            ->distinct()
            ->orderBy('d.name')
            ->getQuery()
            ->getResult();

        foreach($dbResults as $dbResult){
            $results[$dbResult['code']] = new ZipCity($dbResult['name'], $dbResult['code'], $dbResult['admanagerId']);
        }

        return $results;
    }

    /**
     * Remove all accents and prefix from term
     * @param string $term
     * @return string
     */
    private function strStripSpecialCharacters(string $term): string
    {
        // remove special chars
        $charsToStrip =
            ['À', 'Â', 'Ã', 'Ä', 'Å', 'Ç', 'È', 'É', 'Ê', 'Ë', 'Ì', 'Î', 'Ò', 'Ó', 'Ô', 'Õ', 'Ö', 'Ù', 'Ú', 'Û', 'Ü', 'à', 'á', 'â', 'ã',
                'ä', 'å', 'ç', 'è', 'é', 'ê', 'ë', 'ì', 'í', 'î', 'ï', 'ð', 'ò', 'ó', 'ô', 'õ', 'ö', 'ù', 'ú', 'û', 'ü', 'ý', 'ÿ', '-', 'œ', 'Œ', "'"];
        $charsReplaced =
            ['A', 'A', 'A', 'A', 'A', 'C', 'E', 'E', 'E', 'E', 'I', 'I', 'O', 'O', 'O', 'O', 'O', 'U', 'U', 'U', 'U', 'a', 'a', 'a', 'a',
                'a', 'a', 'c', 'e', 'e', 'e', 'e', 'i', 'i', 'i', 'i', 'o', 'o', 'o', 'o', 'o', 'o', 'u', 'u', 'u', 'u', 'y', 'y', ' ', 'oe', 'Oe', " "];

        $term = strtolower(str_replace($charsToStrip, $charsReplaced, $term));

        // remove prefix "la, le"
        $prefix = substr($term, 0, 3);
        if (in_array($prefix, ['le ', 'la '])) {
            $term = substr($term, 3, strlen($term));
        }

        // remove prefix "les"
        $prefix = substr($term, 0, 4);
        if ($prefix === 'les ') {
            $term = substr($term, 4, strlen($term));
        }

        // remove prefix "l'"
        $prefix = substr($term, 0, 2);
        if ($prefix === "l'") {
            $term = substr($term, 2, strlen($term));
        }

        return $term;
    }
}
