<?php

declare(strict_types=1);

namespace App\Command;

use App\Hexagonal\Client\Domain\CreateClientPort;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\{
    Console\Input\InputInterface,
    Console\Output\OutputInterface
};

class UploadClient360Command extends Command
{
    const COMMAND_NAME = 'adfactory:upload-client360';

    private LoggerInterface $logger;
    private CreateClientPort $createClient;

    public function __construct(LoggerInterface $logger, CreateClientPort $createClient)
    {
        $this->logger = $logger;
        $this->createClient = $createClient;
        parent::__construct();
    }

    public function configure()
    {
        $this->setName(self::COMMAND_NAME)
            ->setDescription('Insertion/MAJ clients 360 via csv');
    }

    /**
     * @param InputInterface $input
     * @param OutputInterface $output
     *
     * @return int|null|void
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        try {
            $this->createClient->bulkSave();

            return Command::SUCCESS;
        } catch (\Throwable $e) {
            $this->logger->error(
                'AdFactory Upload Client360 KO',
                [$e->getMessage(), 'File : ' . $e->getFile(), 'Line : ' . $e->getLine()]
            );
            $this->logger->error($e->getTraceAsString());

            return Command::FAILURE;
        }
    }
}
