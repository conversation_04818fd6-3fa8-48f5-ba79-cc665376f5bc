<?php

namespace App\Entity;

use App\Repository\AdUnitRepository;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass=AdUnitRepository::class)
 * @ORM\Table(name="admanager_adunit")
 */
class AdUnit
{
    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\Column(name="label", type="string", length=50, nullable=true)
     */
    private $label;

    /**
     * @ORM\Column(name="id_admanager", type="string", length=50, nullable=true)
     */
    private $admanagerId;


    /**
     * Get the value of id
     */
    public function getId()
    {
        return $this->id;
    }
    
    /**
     * Get the value of label
     */ 
    public function getLabel()
    {
        return $this->label;
    }

    /**
     * Get the value of admanagerId
     */ 
    public function getAdmanagerId()
    {
        return $this->admanagerId;
    }
}
