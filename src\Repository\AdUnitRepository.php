<?php

namespace App\Repository;

use App\Entity\AdUnit;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method AdUnit|null find($id, $lockMode = null, $lockVersion = null)
 * @method AdUnit|null findOneBy(array $criteria, array $orderBy = null)
 * @method AdUnit[]    findAll()
 * @method AdUnit[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class AdUnitRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, AdUnit::class);
    }

}
