<?php

declare(strict_types=1);

namespace App\Entity;

use App\Repository\AdManagerCustomTargetPosRepository;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass=AdManagerCustomTargetPosRepository::class)
 * @ORM\Table(name="admanager_custom_target_pos")
 */
class AdManagerCustomTargetPos
{
    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     */
    private int $id;

    /**
     * @ORM\Column(name="label",type="string", length=255, nullable=true)
     */
    private string $label;

    /**
     * @ORM\Column(name="id_admanager", type="integer")
     */
    private int $admanagerId;

    /**
     * @ORM\Column(name="id_custom_pos", type="integer")
     */
    private int $customPosId;

    /**
     * Get the value of id
     * 
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * Get the value of label
     *
     * @return string
     */
    public function getLabel(): string
    {
        return $this->label;
    }

    /**
     * Get the value of admanagerId
     *
     * @return int
     */
    public function getAdmanagerId(): int
    {
        return $this->admanagerId;
    }

    /**
     * Get the value of customPosId
     *
     * @return int
     */
    public function getCustomPosId(): int
    {
        return $this->customPosId;
    }
}
