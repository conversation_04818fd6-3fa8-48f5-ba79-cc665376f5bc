<?php

declare(strict_types=1);

namespace App\Hexagonal\Forecast\Adapters\Database;

use App\Entity\SmartFormat;
use App\Hexagonal\Forecast\Domain\Database\SmartFormatRepositoryPort;
use Doctrine\Persistence\ManagerRegistry;

final class SmartFormatRepositoryPSQLAdapter implements SmartFormatRepositoryPort
{
    private ManagerRegistry $managerRegistry;

    public function __construct(ManagerRegistry $managerRegistry)
    {
        $this->managerRegistry = $managerRegistry;
    }

    /**
     * @inheritDoc
     */
    public function getByIds(array $ids): array
    {
        $formatRepository = $this->managerRegistry->getRepository(SmartFormat::class);
        return $formatRepository->findBy(['id' => $ids]);
    }
}