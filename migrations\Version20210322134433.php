<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210322134433 extends AbstractMigration
{
    public function getDescription() : string
    {
        return '';
    }

    public function up(Schema $schema) : void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE SEQUENCE counts_id_seq INCREMENT BY 1 MINVALUE 1 START 10000');
        $this->addSql('CREATE TABLE counts (id INT NOT NULL, created_date TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, product_id INT NOT NULL, product_name VARCHAR(40) NOT NULL, product_id_sf VARCHAR(40) NOT NULL, broadcast_medium JSON NOT NULL, project_type JSON NOT NULL, periodicity VARCHAR(40) NOT NULL, start_date TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, end_date TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, count_item JSON NOT NULL, user_id INT NOT NULL, market VARCHAR(40) NOT NULL, client_id VARCHAR(40) NULL, total_imp_purchased INT NOT NULL, total_budget INT NOT NULL, PRIMARY KEY(id))');
        $this->addSql('COMMENT ON COLUMN counts.broadcast_medium IS \'(DC2Type:json_array)\'');
        $this->addSql('COMMENT ON COLUMN counts.project_type IS \'(DC2Type:json_array)\'');
        $this->addSql('COMMENT ON COLUMN counts.count_item IS \'(DC2Type:json_array)\'');
    }

    public function down(Schema $schema) : void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('DROP TABLE counts');
    }
}
